[2025-07-28T20:08:54.239316 17736] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T20:08:54.239360 15756] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T20:08:54.239446 22180] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T20:08:54.239509 17736] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T20:08:54.239538 15756] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T20:08:54.239533 17508] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T20:08:54.239539 17736] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T20:08:54.239539 17736] Config: (default) base_dir = 
[2025-07-28T20:08:54.239539 17736] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T20:08:54.239539 17736] Config: (default) compiler = 
[2025-07-28T20:08:54.239539 17736] Config: (default) compiler_check = mtime
[2025-07-28T20:08:54.239539 17736] Config: (default) compiler_type = auto
[2025-07-28T20:08:54.239539 17736] Config: (default) compression = true
[2025-07-28T20:08:54.239539 17736] Config: (default) compression_level = 0
[2025-07-28T20:08:54.239539 17736] Config: (default) cpp_extension = 
[2025-07-28T20:08:54.239539 17736] Config: (default) debug = false
[2025-07-28T20:08:54.239539 17736] Config: (default) debug_dir = 
[2025-07-28T20:08:54.239539 17736] Config: (default) debug_level = 2
[2025-07-28T20:08:54.239539 17736] Config: (default) depend_mode = false
[2025-07-28T20:08:54.239539 17736] Config: (default) direct_mode = true
[2025-07-28T20:08:54.239539 17736] Config: (default) disable = false
[2025-07-28T20:08:54.239539 17736] Config: (default) extra_files_to_hash = 
[2025-07-28T20:08:54.239539 17736] Config: (default) file_clone = false
[2025-07-28T20:08:54.239539 17736] Config: (default) hard_link = false
[2025-07-28T20:08:54.239539 17736] Config: (default) hash_dir = true
[2025-07-28T20:08:54.239539 17736] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T20:08:54.239539 17736] Config: (default) ignore_options = 
[2025-07-28T20:08:54.239539 17736] Config: (default) inode_cache = false
[2025-07-28T20:08:54.239539 17736] Config: (default) keep_comments_cpp = false
[2025-07-28T20:08:54.239539 17736] Config: (environment) log_file = C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI\ccache-17480.txt
[2025-07-28T20:08:54.239539 17736] Config: (default) max_files = 0
[2025-07-28T20:08:54.239539 17736] Config: (default) max_size = 5.0 GiB
[2025-07-28T20:08:54.239539 17736] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T20:08:54.239539 17736] Config: (default) namespace = 
[2025-07-28T20:08:54.239539 17736] Config: (default) path = 
[2025-07-28T20:08:54.239539 17736] Config: (default) pch_external_checksum = false
[2025-07-28T20:08:54.239539 17736] Config: (default) prefix_command = 
[2025-07-28T20:08:54.239539 17736] Config: (default) prefix_command_cpp = 
[2025-07-28T20:08:54.239539 17736] Config: (default) read_only = false
[2025-07-28T20:08:54.239539 17736] Config: (default) read_only_direct = false
[2025-07-28T20:08:54.239539 17736] Config: (default) recache = false
[2025-07-28T20:08:54.239539 17736] Config: (default) remote_only = false
[2025-07-28T20:08:54.239539 17736] Config: (default) remote_storage = 
[2025-07-28T20:08:54.239539 17736] Config: (default) reshare = false
[2025-07-28T20:08:54.239539 17736] Config: (default) run_second_cpp = true
[2025-07-28T20:08:54.239539 17736] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T20:08:54.239539 17736] Config: (default) stats = true
[2025-07-28T20:08:54.239539 17736] Config: (default) stats_log = 
[2025-07-28T20:08:54.239539 17736] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T20:08:54.239539 17736] Config: (default) umask = 
[2025-07-28T20:08:54.239646 17736] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o __loader.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace static_src\OL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace __loader.c
default) ignore_headers_in_manifest = 
[2025-07-28T20:08:54.239533 17508] Config: (default) ignore_options = 
[2025-07-28T20:08:54.239533 17508] Config: (default) inode_cache = false
[2025-07-28T20:08:54.239533 17508] Config: (default) keep_comments_cpp = false
[2025-07-28T20:08:54.239533 17508] Config: (environment) log_file = C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI\ccache-17480.txt
[2025-07-28T20:08:54.239533 17508] Config: (default) max_files = 0
[2025-07-28T20:08:54.239533 17508] Config: (default) max_size = 5.0 GiB
[2025-07-28T20:08:54.239533 17508] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T20:08:54.239533 17508] Config: (default) namespace = 
[2025-07-28T20:08:54.239533 17508] Config: (default) path = 
[2025-07-28T20:08:54.239533 17508] Config: (default) pch_external_checksum = false
[2025-07-28T20:08:54.239533 17508] Config: (default) prefix_command = 
[2025-07-28T20:08:54.239533 17508] Config: (default) prefix_command_cpp = 
[2025-07-28T20:08:54.239533 17508] Config: (default) read_only = false
[2025-07-28T20:08:54.239533 17508] Config: (default) read_only_direct = false
[2025-07-28T20:08:54.239533 17508] Config: (default) recache = false
[2025-07-28T20:08:54.239533 17508] Config: (default) remote_only = false
[2025-07-28T20:08:54.239533 17508] Config: (default) remote_storage = 
[2025-07-28T20:08:54.239533 17508] Config: (default) reshare = false
[2025-07-28T20:08:54.239533 17508] Config: (default) run_second_cpp = true
[2025-07-28T20:08:54.239533 17508] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T20:08:54.239533 17508] Config: (default) stats = true
[2025-07-28T20:08:54.239533 17508] Config: (default) stats_log = 
[2025-07-28T20:08:54.239533 17508] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T20:08:54.239533 17508] Config: (default) umask = 
[2025-07-28T20:08:54.239812 17508] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o __constants.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace __constants.c
54.239568 22180] Config: (default) debug = false
[2025-07-28T20:08:54.239568 22180] Config: (default) debug_dir = 
[2025-07-28T20:08:54.239568 22180] Config: (default) debug_level = 2
[2025-07-28T20:08:54.239568 22180] Config: (default) depend_mode = false
[2025-07-28T20:08:54.239568 22180] Config: (default) direct_mode = true
[2025-07-28T20:08:54.239568 22180] Config: (default) disable = false
[2025-07-28T20:08:54.239568 22180] Config: (default) extra_files_to_hash = 
[2025-07-28T20:08:54.239568 22180] Config: (default) file_clone = false
[2025-07-28T20:08:54.239568 22180] Config: (default) hard_link = false
[2025-07-28T20:08:54.239568 22180] Config: (default) hash_dir = true
[2025-07-28T20:08:54.239568 22180] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T20:08:54.239568 22180] Config: (default) ignore_options = 
[2025-07-28T20:08:54.239568 22180] Config: (default) inode_cache = false
[2025-07-28T20:08:54.239568 22180] Config: (default) keep_comments_cpp = false
[2025-07-28T20:08:54.239568 22180] Config: (environment) log_file = C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI\ccache-17480.txt
[2025-07-28T20:08:54.239568 22180] Config: (default) max_files = 0
[2025-07-28T20:08:54.239568 22180] Config: (default) max_size = 5.0 GiB
[2025-07-28T20:08:54.239568 22180] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T20:08:54.239568 22180] Config: (default) namespace = 
[2025-07-28T20:08:54.239568 22180] Config: (default) path = 
[2025-07-28T20:08:54.239568 22180] Config: (default) pch_external_checksum = false
[2025-07-28T20:08:54.239568 22180] Config: (default) prefix_command = 
[2025-07-28T20:08:54.239568 22180] Config: (default) prefix_command_cpp = 
[2025-07-28T20:08:54.239568 22180] Config: (default) read_only = false
[2025-07-28T20:08:54.239568 22180] Config: (default) read_only_direct = false
[2025-07-28T20:08:54.239568 22180] Config: (default) recache = false
[2025-07-28T20:08:54.239568 22180] Config: (default) remote_only = false
[2025-07-28T20:08:54.239568 22180] Config: (default) remote_storage = 
[2025-07-28T20:08:54.239568 22180] Config: (default) reshare = false
[2025-07-28T20:08:54.239568 22180] Config: (default) run_second_cpp = true
[2025-07-28T20:08:54.239568 22180] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T20:08:54.239568 22180] Config: (default) stats = true
[2025-07-28T20:08:54.239568 22180] Config: (default) stats_log = 
[2025-07-28T20:08:54.239568 22180] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T20:08:54.239568 22180] Config: (default) umask = 
[2025-07-28T20:08:54.239858 22180] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o module.tkinter-preLoad.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace module.tkinter-preLoad.c
[2025-07-28T20:08:54.244188 22180] Hostname: Endorfina
[2025-07-28T20:08:54.244215 22180] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.244213 2308 ] Hostname: Endorfina
[2025-07-28T20:08:54.244241 22180] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.244243 2308 ] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.244248 22180] Compiler type: gcc
[2025-07-28T20:08:54.244265 2308 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.244275 2308 ] Compiler type: gcc
[2025-07-28T20:08:54.244476 22180] Detected input file: module.tkinter-preLoad.c
[2025-07-28T20:08:54.244498 2308 ] Detected input file: __helpers.c
[2025-07-28T20:08:54.244512 17508] Hostname: Endorfina
[2025-07-28T20:08:54.244531 17508] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.244549 17508] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.244555 17508] Compiler type: gcc
[2025-07-28T20:08:54.244579 22180] Source file: module.tkinter-preLoad.c
[2025-07-28T20:08:54.244586 22180] Object file: module.tkinter-preLoad.o
[2025-07-28T20:08:54.244592 2308 ] Source file: __helpers.c
[2025-07-28T20:08:54.244600 2308 ] Object file: __helpers.o
[2025-07-28T20:08:54.244742 22180] Trying direct lookup
[2025-07-28T20:08:54.244762 17508] Detected input file: __constants.c
[2025-07-28T20:08:54.244894 17508] Source file: __constants.c
[2025-07-28T20:08:54.244902 15756] Hostname: Endorfina
nts.o
[2025-07-28T20:08:54.244909 2308 ] Trying direct lookup
[2025-07-28T20:08:54.244922 15756] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.244941 15756] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.244947 15756] Compiler type: gcc
[2025-07-28T20:08:54.244945 17736] Hostname: Endorfina
[2025-07-28T20:08:54.244963 17736] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.244980 17736] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.245016 17736] Compiler type: gcc
[2025-07-28T20:08:54.245092 17508] Trying direct lookup
[2025-07-28T20:08:54.245143 22180] Manifest key: 47c2gei5elgobcc27sbrnk6n4preijbbq
[2025-07-28T20:08:54.245161 15756] Detected input file: static_src\MainProgram.c
[2025-07-28T20:08:54.245228 17736] Detected input file: __loader.c
[2025-07-28T20:08:54.245248 2308 ] Manifest key: 1009l5muslge8dc2g43tr5q6deforpeeq
[2025-07-28T20:08:54.245282 15756] Source file: static_src\MainProgram.c
[2025-07-28T20:08:54.245289 15756] Object file: static_src\MainProgram.o
[2025-07-28T20:08:54.245327 17736] Source file: __loader.c
[2025-07-28T20:08:54.245329 14836] Hostname: Endorfina
.o
[2025-07-28T20:08:54.245350 14836] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.245374 14836] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.245383 14836] Compiler type: gcc
[2025-07-28T20:08:54.245423 15756] Trying direct lookup
[2025-07-28T20:08:54.245467 17736] Trying direct lookup
[2025-07-28T20:08:54.245505 17508] Manifest key: af3fac3qnecpsq4bhsnsslqm0vep7nf5a
[2025-07-28T20:08:54.245663 14836] Detected input file: module.__main__.c
[2025-07-28T20:08:54.245791 14836] Source file: module.__main__.c
[2025-07-28T20:08:54.245804 14836] Object file: module.__main__.o
[2025-07-28T20:08:54.245918 15756] Manifest key: 4eednbpuq53c7r189pehaepe9dtqdh4p2
[2025-07-28T20:08:54.245975 17736] Manifest key: dc0cstlh8je8tst4k4j8fl0sldpki2mg0
[2025-07-28T20:08:54.246008 14836] Trying direct lookup
[2025-07-28T20:08:54.246366 17736] No dc0cstlh8je8tst4k4j8fl0sldpki2mg0 in local storage
[2025-07-28T20:08:54.247354 17736] Running preprocessor
[2025-07-28T20:08:54.247629 17736] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace -E -o C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\ccache/tmp/cpp_stdout.tmp.e85DV2.i __loader.c
[2025-07-28T20:08:54.250684 14836] Manifest key: 7c7fvutbc8o9ddqae1ajk9jl27gg2qc72
[2025-07-28T20:08:54.251398 14836] No 7c7fvutbc8o9ddqae1ajk9jl27gg2qc72 in local storage
[2025-07-28T20:08:54.251634 15756] Retrieved 4eednbpuq53c7r189pehaepe9dtqdh4p2 from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/e/ednbpuq53c7r189pehaepe9dtqdh4p2M)
[2025-07-28T20:08:54.251678 2308 ] Retrieved 1009l5muslge8dc2g43tr5q6deforpeeq from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/0/09l5muslge8dc2g43tr5q6deforpeeqM)
[2025-07-28T20:08:54.251708 17508] Retrieved af3fac3qnecpsq4bhsnsslqm0vep7nf5a from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/f/3fac3qnecpsq4bhsnsslqm0vep7nf5aM)
[2025-07-28T20:08:54.251796 22180] Retrieved 47c2gei5elgobcc27sbrnk6n4preijbbq from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/7/c2gei5elgobcc27sbrnk6n4preijbbqM)
[2025-07-28T20:08:54.252110 14836] Running preprocessor
[2025-07-28T20:08:54.252413 14836] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace -E -o C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\ccache/tmp/cpp_stdout.tmp.Ig0rfr.i module.__main__.c
[2025-07-28T20:08:54.988837 6696 ] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T20:08:54.988886 6696 ] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T20:08:54.988896 6696 ] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T20:08:54.988896 6696 ] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) base_dir = 
[2025-07-28T20:08:54.988896 6696 ] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T20:08:54.988896 6696 ] Config: (default) compiler = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) compiler_check = mtime
[2025-07-28T20:08:54.988896 6696 ] Config: (default) compiler_type = auto
[2025-07-28T20:08:54.988896 6696 ] Config: (default) compression = true
[2025-07-28T20:08:54.988896 6696 ] Config: (default) compression_level = 0
[2025-07-28T20:08:54.988896 6696 ] Config: (default) cpp_extension = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) debug = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) debug_dir = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) debug_level = 2
[2025-07-28T20:08:54.988896 6696 ] Config: (default) depend_mode = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) direct_mode = true
[2025-07-28T20:08:54.988896 6696 ] Config: (default) disable = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) extra_files_to_hash = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) file_clone = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) hard_link = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) hash_dir = true
[2025-07-28T20:08:54.988896 6696 ] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) ignore_options = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) inode_cache = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) keep_comments_cpp = false
[2025-07-28T20:08:54.988896 6696 ] Config: (environment) log_file = C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI\ccache-17480.txt
[2025-07-28T20:08:54.988896 6696 ] Config: (default) max_files = 0
[2025-07-28T20:08:54.988896 6696 ] Config: (default) max_size = 5.0 GiB
[2025-07-28T20:08:54.988896 6696 ] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T20:08:54.988896 6696 ] Config: (default) namespace = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) path = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) pch_external_checksum = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) prefix_command = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) prefix_command_cpp = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) read_only = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) read_only_direct = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) recache = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) remote_only = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) remote_storage = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) reshare = false
[2025-07-28T20:08:54.988896 6696 ] Config: (default) run_second_cpp = true
[2025-07-28T20:08:54.988896 6696 ] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T20:08:54.988896 6696 ] Config: (default) stats = true
[2025-07-28T20:08:54.988896 6696 ] Config: (default) stats_log = 
[2025-07-28T20:08:54.988896 6696 ] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T20:08:54.988896 6696 ] Config: (default) umask = 
[2025-07-28T20:08:54.988971 6696 ] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\CompiledFunctionType.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace static_src\\CompiledFunctionType.c
[2025-07-28T20:08:54.992366 6696 ] Hostname: Endorfina
[2025-07-28T20:08:54.992392 6696 ] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.BUI
[2025-07-28T20:08:54.992410 6696 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:08:54.992416 6696 ] Compiler type: gcc
[2025-07-28T20:08:54.992628 6696 ] Detected input file: static_src\CompiledFunctionType.c
[2025-07-28T20:08:54.992746 6696 ] Source file: static_src\CompiledFunctionType.c
[2025-07-28T20:08:54.992754 6696 ] Object file: static_src\CompiledFunctionType.o
[2025-07-28T20:08:54.992894 6696 ] Trying direct lookup
[2025-07-28T20:08:54.993261 6696 ] Manifest key: 59318m473dbufd5badsg0h1eqff5q67uk
[2025-07-28T20:08:55.001540 6696 ] Retrieved 59318m473dbufd5badsg0h1eqff5q67uk from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/9/318m473dbufd5badsg0h1eqff5q67ukM)
[2025-07-28T20:08:55.471815 17508] Got result key from manifest
[2025-07-28T20:08:55.471843 17508] Result key: c13444ut20eutjn45i1eu38v86rr4v00c
[2025-07-28T20:08:55.471865 2308 ] Got result key from manifest
[2025-07-28T20:08:55.471872 15756] Got result key from manifest
[2025-07-28T20:08:55.471888 2308 ] Result key: 845afba09tbfbjkasedqu07ck3ersalhm
[2025-07-28T20:08:55.471893 22180] Got result key from manifest
j8924i2bt6hur6u
[2025-07-28T20:08:55.471921 22180] Result key: 19cahajp78e3pv9dr7d8n7u915d4krj4k
[2025-07-28T20:08:55.477303 2308 ] Retrieved 845afba09tbfbjkasedqu07ck3ersalhm from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/4/5afba09tbfbjkasedqu07ck3ersalhmR)
[2025-07-28T20:08:55.477671 2308 ] Reading embedded entry #0 .o (2651 bytes)
[2025-07-28T20:08:55.477691 2308 ] Writing to __helpers.o
[2025-07-28T20:08:55.477695 15756] Retrieved 6545bk0ghmro6cmi61j8924i2bt6hur6u from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/5/45bk0ghmro6cmi61j8924i2bt6hur6uR)
[2025-07-28T20:08:55.477937 22180] Reading embedded entry #0 .o (49380 bytes)
[2025-07-28T20:08:55.477954 22180] Writing to module.tkinter-preLoad.o
ytes)
[2025-07-28T20:08:55.477968 15756] Writing to static_src\MainProgram.o
[2025-07-28T20:08:55.478193 17508] Retrieved c13444ut20eutjn45i1eu38v86rr4v00c from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/1/3444ut20eutjn45i1eu38v86rr4v00cR)
[2025-07-28T20:08:55.478227 2308 ] Succeeded getting cached result
[2025-07-28T20:08:55.478263 2308 ] Result: direct_cache_hit
[2025-07-28T20:08:55.478272 2308 ] Result: local_storage_hit
[2025-07-28T20:08:55.478280 2308 ] Result: local_storage_read_hit
[2025-07-28T20:08:55.478286 2308 ] Result: local_storage_read_hit
[2025-07-28T20:08:55.478312 2308 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/4/stats.lock
[2025-07-28T20:08:55.478480 22180] Succeeded getting cached result
[2025-07-28T20:08:55.478516 22180] Result: direct_cache_hit
.o (17767 bytes)
[2025-07-28T20:08:55.478523 22180] Result: local_storage_hit
[2025-07-28T20:08:55.478528 22180] Result: local_storage_read_hit
[2025-07-28T20:08:55.478530 17508] Writing to __constants.o
_hit
[2025-07-28T20:08:55.478564 22180] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/4/stats.lock
[2025-07-28T20:08:55.478705 15756] Succeeded getting cached result
[2025-07-28T20:08:55.478742 15756] Result: direct_cache_hit
[2025-07-28T20:08:55.478748 15756] Result: local_storage_hit
[2025-07-28T20:08:55.478754 15756] Result: local_storage_read_hit
[2025-07-28T20:08:55.478759 15756] Result: local_storage_read_hit
[2025-07-28T20:08:55.478777 15756] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/c/stats.lock
[2025-07-28T20:08:55.479172 17508] Succeeded getting cached result
[2025-07-28T20:08:55.479223 17508] Result: direct_cache_hit
[2025-07-28T20:08:55.479231 17508] Result: local_storage_hit
[2025-07-28T20:08:55.479236 17508] Result: local_storage_read_hit
[2025-07-28T20:08:55.479241 17508] Result: local_storage_read_hit
[2025-07-28T20:08:55.479262 17508] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/4/stats.lock
[2025-07-28T20:08:55.479273 22180] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/4/stats.lock
[2025-07-28T20:08:55.479303 2308 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/4/stats.lock
[2025-07-28T20:08:55.479467 15756] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/c/stats.lock
[2025-07-28T20:08:55.479866 17508] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/4/stats.lock
[2025-07-28T20:08:55.485830 22180] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/4/stats.lock
[2025-07-28T20:08:55.485923 17508] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/4/stats.lock
[2025-07-28T20:08:55.485996 22180] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/4/stats.lock
[2025-07-28T20:08:55.486074 15756] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/c/stats.lock
[2025-07-28T20:08:55.486083 17508] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/4/stats.lock
[2025-07-28T20:08:55.486157 15756] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/c/stats.lock
[2025-07-28T20:08:55.486483 2308 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/4/stats.lock
[2025-07-28T20:08:55.486671 2308 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/4/stats.lock
[2025-07-28T20:08:55.514892 6696 ] Got result key from manifest
[2025-07-28T20:08:55.514924 6696 ] Result key: d2bflkrv748r1fi4qcj7do1mifdc0n926
[2025-07-28T20:08:55.522567 6696 ] Retrieved d2bflkrv748r1fi4qcj7do1mifdc0n926 from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/2/bflkrv748r1fi4qcj7do1mifdc0n926R)
[2025-07-28T20:08:55.525214 6696 ] Reading embedded entry #0 .o (3908920 bytes)
[2025-07-28T20:08:55.525248 6696 ] Writing to static_src\CompiledFunctionType.o
[2025-07-28T20:08:55.526700 6696 ] Succeeded getting cached result
[2025-07-28T20:08:55.526903 6696 ] Result: direct_cache_hit
[2025-07-28T20:08:55.526912 6696 ] Result: local_storage_hit
[2025-07-28T20:08:55.526920 6696 ] Result: local_storage_read_hit
[2025-07-28T20:08:55.526927 6696 ] Result: local_storage_read_hit
[2025-07-28T20:08:55.526953 6696 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/8/stats.lock
[2025-07-28T20:08:55.527871 6696 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/8/stats.lock
[2025-07-28T20:08:55.534180 6696 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/8/stats.lock
[2025-07-28T20:08:55.534266 6696 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/8/stats.lock
[2025-07-28T20:08:57.252877 17736] Got result key from preprocessor
[2025-07-28T20:08:57.252900 17736] Result key: c7108j1tcln2nrqj1nlspoqbkv43o6080
[2025-07-28T20:08:57.252976 17736] No c7108j1tcln2nrqj1nlspoqbkv43o6080 in local storage
[2025-07-28T20:08:57.252991 17736] Running real compiler
[2025-07-28T20:08:57.253435 17736] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace -c -fdiagnostics-color -o __loader.o __loader.c
[2025-07-28T20:08:57.531455 14836] Got result key from preprocessor
[2025-07-28T20:08:57.531487 14836] Result key: 5bbcgmavm9e33ja6djjnims9ahhhul0oo
[2025-07-28T20:08:57.531578 14836] No 5bbcgmavm9e33ja6djjnims9ahhhul0oo in local storage
[2025-07-28T20:08:57.531594 14836] Running real compiler
[2025-07-28T20:08:57.532052 14836] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=152 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\include -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\libbacktrace -c -fdiagnostics-color -o module.__main__.o module.__main__.c
[2025-07-28T20:08:57.996081 17736] Using Zstandard with default compression level 1
[2025-07-28T20:08:57.996152 17736] Storing embedded entry #0 .o (27263 bytes) from __loader.o
[2025-07-28T20:08:57.997063 17736] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c7.lock
[2025-07-28T20:08:57.997238 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c7.lock
[2025-07-28T20:08:57.997502 17736] Stored c7108j1tcln2nrqj1nlspoqbkv43o6080 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/7/108j1tcln2nrqj1nlspoqbkv43o6080R)
[2025-07-28T20:08:57.997561 17736] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T20:08:57.997693 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T20:08:58.003553 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T20:08:58.003711 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-07-28T20:08:58.003724 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c7.lock
[2025-07-28T20:08:58.003781 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c7.lock
[2025-07-28T20:08:58.016215 17736] Added result key to manifest dc0cstlh8je8tst4k4j8fl0sldpki2mg0
[2025-07-28T20:08:58.016237 17736] Using Zstandard with default compression level 1
[2025-07-28T20:08:58.017044 17736] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_dc.lock
[2025-07-28T20:08:58.017200 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_dc.lock
[2025-07-28T20:08:58.017756 17736] Stored dc0cstlh8je8tst4k4j8fl0sldpki2mg0 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/c/0cstlh8je8tst4k4j8fl0sldpki2mg0M)
[2025-07-28T20:08:58.017816 17736] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-07-28T20:08:58.017948 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-07-28T20:08:58.023350 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-07-28T20:08:58.023458 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-07-28T20:08:58.023469 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_dc.lock
[2025-07-28T20:08:58.023544 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_dc.lock
[2025-07-28T20:08:58.023653 17736] Result: cache_miss
[2025-07-28T20:08:58.023660 17736] Result: direct_cache_miss
[2025-07-28T20:08:58.023664 17736] Result: local_storage_miss
[2025-07-28T20:08:58.023669 17736] Result: local_storage_read_miss
[2025-07-28T20:08:58.023673 17736] Result: local_storage_read_miss
[2025-07-28T20:08:58.023678 17736] Result: local_storage_write
[2025-07-28T20:08:58.023683 17736] Result: local_storage_write
[2025-07-28T20:08:58.023687 17736] Result: preprocessed_cache_miss
[2025-07-28T20:08:58.023700 17736] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-07-28T20:08:58.023887 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-07-28T20:08:58.029096 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-07-28T20:08:58.029216 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-07-28T20:08:58.029290 17736] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:08:58.029438 17736] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:08:58.088248 17736] No automatic cleanup needed (size 60.8 MiB, files 400, max size 5.0 GiB)
[2025-07-28T20:08:58.088273 17736] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:08:58.088369 17736] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:08:58.088589 17736] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.5XfICI.tmp
[2025-07-28T20:08:58.088705 17736] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.sbfV4O.tmp
[2025-07-28T20:08:58.088805 17736] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.PghlZY.tmp
[2025-07-28T20:08:58.089364 17736] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.e85DV2.i
[2025-07-28T20:09:00.725641 14836] Using Zstandard with default compression level 1
[2025-07-28T20:09:00.726293 14836] Storing embedded entry #0 .o (3257505 bytes) from module.__main__.o
[2025-07-28T20:09:00.731275 14836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_5b.lock
[2025-07-28T20:09:00.731446 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_5b.lock
[2025-07-28T20:09:00.731701 14836] Stored 5bbcgmavm9e33ja6djjnims9ahhhul0oo in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/b/bcgmavm9e33ja6djjnims9ahhhul0ooR)
[2025-07-28T20:09:00.731778 14836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/stats.lock
[2025-07-28T20:09:00.731932 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/stats.lock
[2025-07-28T20:09:00.732672 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/stats.lock
[2025-07-28T20:09:00.732743 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/stats.lock
[2025-07-28T20:09:00.732772 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_5b.lock
[2025-07-28T20:09:00.732817 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_5b.lock
[2025-07-28T20:09:00.744237 14836] Added result key to manifest 7c7fvutbc8o9ddqae1ajk9jl27gg2qc72
[2025-07-28T20:09:00.744257 14836] Using Zstandard with default compression level 1
[2025-07-28T20:09:00.745020 14836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7c.lock
[2025-07-28T20:09:00.745174 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7c.lock
[2025-07-28T20:09:00.745684 14836] Stored 7c7fvutbc8o9ddqae1ajk9jl27gg2qc72 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/7fvutbc8o9ddqae1ajk9jl27gg2qc72M)
[2025-07-28T20:09:00.745834 14836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-07-28T20:09:00.745995 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-07-28T20:09:00.746749 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-07-28T20:09:00.746836 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-07-28T20:09:00.746845 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7c.lock
[2025-07-28T20:09:00.746888 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7c.lock
[2025-07-28T20:09:00.746990 14836] Result: cache_miss
[2025-07-28T20:09:00.746999 14836] Result: direct_cache_miss
[2025-07-28T20:09:00.747004 14836] Result: local_storage_miss
[2025-07-28T20:09:00.747009 14836] Result: local_storage_read_miss
[2025-07-28T20:09:00.747013 14836] Result: local_storage_read_miss
[2025-07-28T20:09:00.747017 14836] Result: local_storage_write
[2025-07-28T20:09:00.747021 14836] Result: local_storage_write
[2025-07-28T20:09:00.747025 14836] Result: preprocessed_cache_miss
[2025-07-28T20:09:00.747036 14836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/4/stats.lock
[2025-07-28T20:09:00.751740 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/4/stats.lock
[2025-07-28T20:09:00.756052 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/4/stats.lock
[2025-07-28T20:09:00.756144 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/4/stats.lock
[2025-07-28T20:09:00.756195 14836] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:09:00.756328 14836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:09:00.757007 14836] No automatic cleanup needed (size 63.8 MiB, files 402, max size 5.0 GiB)
[2025-07-28T20:09:00.757027 14836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:09:00.757086 14836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-07-28T20:09:00.757245 14836] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.jMfBcU.tmp
[2025-07-28T20:09:00.757352 14836] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.K39GcG.tmp
[2025-07-28T20:09:00.757450 14836] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.2AINev.tmp
[2025-07-28T20:09:00.758534 14836] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.Ig0rfr.i
