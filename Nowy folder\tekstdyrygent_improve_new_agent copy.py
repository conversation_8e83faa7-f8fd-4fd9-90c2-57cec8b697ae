import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import re
from collections import Counter

class TekstDyrygent:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TekstDyrygent - Zaawansowany Notatnik")
        self.root.geometry("1000x700")
        
        # Domyślne ustawienia
        self.font_size = 12
        self.font_family = "Arial"
        self.text_color = "black"
        self.bg_color = "white"
        self.cursor_color = "black"
        self.show_line_numbers = False
        self.show_duplicates = False
        self.min_duplicate_length = 3  # Minimalna długość duplikatów
        self.available_fonts = self.get_available_fonts()
        
        self.setup_ui()
        self.bind_shortcuts()
        
    def get_available_fonts(self):
        """Pobiera dostępne fonty systemowe i z foldera fonts (jeśli istnieje)"""
        import tkinter.font as tkFont
        import os
        
        # Fonty systemowe
        system_fonts = list(tkFont.families())
        
        # Pr<PERSON><PERSON> załadowania fontów z foldera fonts
        fonts_folder = os.path.join(os.getcwd(), "fonts")
        custom_fonts = []
        
        if os.path.exists(fonts_folder):
            for file in os.listdir(fonts_folder):
                if file.lower().endswith(('.ttf', '.otf')):
                    font_name = os.path.splitext(file)[0]
                    custom_fonts.append(font_name)
        
        # Połącz wszystkie fonty i usuń duplikaty
        all_fonts = sorted(list(set(system_fonts + custom_fonts)))
        return all_fonts
        
    def setup_ui(self):
        # Menu
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Otwórz", command=self.open_file)
        file_menu.add_command(label="Zapisz", command=self.save_file)

        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Pokaż/Ukryj pasek komend", command=self.toggle_command_bar)

        info_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Info", menu=info_menu)
        info_menu.add_command(label="Instrukcja", command=self.show_help)

        # Główny kontener z układem grid dla lepszej kontroli
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        main_container = tk.Frame(self.root)
        main_container.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)

        # Frame dla numeracji i tekstu - główny obszar
        text_frame = tk.Frame(main_container)
        text_frame.grid(row=0, column=0, sticky="nsew", pady=(0,5))
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(1, weight=1)

        # Numeracja linii - oddzielne okno z możliwością kopiowania
        self.line_frame = tk.Frame(text_frame, width=60, bg='lightgray', relief=tk.SUNKEN, bd=1)

        self.line_numbers = tk.Text(self.line_frame, width=5, padx=3, takefocus=0,
                                   border=0, state='normal', wrap='none',
                                   bg='lightgray', fg='black', cursor='arrow',
                                   font=(self.font_family, self.font_size))
        self.line_numbers.pack(fill=tk.BOTH, expand=True)

        # Blokowanie edycji numeracji (tylko kopiowanie)
        def block_edit(event):
            if event.keysym in ['BackSpace', 'Delete'] or event.char.isprintable():
                return "break"
        self.line_numbers.bind('<Key>', block_edit)

        # Główne pole tekstowe
        self.text_area = tk.Text(text_frame, wrap=tk.WORD, undo=True,
                                font=(self.font_family, self.font_size),
                                fg=self.text_color, bg=self.bg_color,
                                insertbackground=self.cursor_color)
        self.text_area.grid(row=0, column=1, sticky="nsew")

        # Scrollbar
        self.scrollbar = tk.Scrollbar(text_frame)
        self.scrollbar.grid(row=0, column=2, sticky="ns")
        self.text_area.config(yscrollcommand=self.scrollbar.set)
        self.scrollbar.config(command=self.on_scroll)

        # Pasek przycisków - zawsze widoczny na dole
        button_frame = tk.Frame(main_container, relief=tk.RAISED, bd=1)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(0,2))

        # Przyciski
        tk.Button(button_frame, text="Rozmiar tekstu", command=self.change_font_size).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Font", command=self.change_font_family).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor tekstu", command=self.change_text_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor tła", command=self.change_bg_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor kursora", command=self.change_cursor_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Numeracja", command=self.toggle_line_numbers).pack(side=tk.LEFT, padx=2)

        self.duplicate_button = tk.Button(button_frame, text="Duplikaty", command=self.toggle_duplicates)
        self.duplicate_button.pack(side=tk.LEFT, padx=2)

        tk.Button(button_frame, text="Raport", command=self.show_report).pack(side=tk.LEFT, padx=2)

        # Pole wyszukiwania
        tk.Label(button_frame, text="Znajdź:").pack(side=tk.LEFT, padx=(10,2))
        self.search_entry = tk.Entry(button_frame, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        self.search_entry.bind('<Return>', self.search_text)

        # Pasek komend - zawsze widoczny
        self.command_frame = tk.Frame(main_container, relief=tk.RAISED, bd=1)
        self.command_frame.grid(row=2, column=0, sticky="ew", pady=(0,2))

        tk.Label(self.command_frame, text="Komenda:").pack(side=tk.LEFT, padx=(5,0))
        self.command_entry = tk.Entry(self.command_frame)
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5,5))
        self.command_entry.bind('<Return>', self.execute_command)

        # Pasek statusu - zawsze widoczny na samym dole
        self.status_bar = tk.Label(main_container, text="Znaków: 0 | Linia: 1 | Kolumna: 1 | Zaznaczenie: 0",
                                  relief=tk.SUNKEN, anchor=tk.W, bd=1)
        self.status_bar.grid(row=3, column=0, sticky="ew")

        # Bindy dla aktualizacji statusu
        self.text_area.bind('<KeyRelease>', self.update_status)
        self.text_area.bind('<ButtonRelease>', self.update_status)
        self.text_area.bind('<Motion>', self.update_status)
        self.text_area.bind('<Button-1>', self.on_click)
        self.text_area.bind('<Double-Button-1>', self.on_double_click)

        # Bind dla zmiany rozmiaru okna
        self.root.bind('<Configure>', self.on_window_configure)

        self.update_line_numbers()

    def on_scroll(self, *args):
        """Synchronizacja przewijania między tekstem a numeracją"""
        self.text_area.yview(*args)
        if self.show_line_numbers:
            self.line_numbers.yview(*args)

    def on_window_configure(self, event):
        """Obsługa zmiany rozmiaru okna - zapewnia widoczność pasków"""
        if event.widget == self.root:
            # Wymuszenie aktualizacji układu
            self.root.update_idletasks()

    def bind_shortcuts(self):
        # Skróty klawiszowe
        self.root.bind('<Control-f>', lambda e: self.format_selection('bold'))
        self.root.bind('<Control-i>', lambda e: self.format_selection('italic'))
        self.root.bind('<Control-y>', lambda e: self.format_selection('yellow'))
        self.root.bind('<Control-r>', lambda e: self.format_selection('red'))
        self.root.bind('<Control-b>', lambda e: self.format_selection('blue'))
        self.root.bind('<Control-g>', lambda e: self.format_selection('green'))
        self.root.bind('<Control-p>', lambda e: self.format_selection('purple'))
        self.root.bind('<Control-0>', lambda e: self.format_selection('clear'))
        
        self.root.bind('<Control-Alt-f>', lambda e: self.bold_all_text())
        self.root.bind('<Control-q>', lambda e: self.jump_to_start())
        self.root.bind('<Control-w>', lambda e: self.jump_word_forward())
        self.root.bind('<Control-s>', lambda e: self.jump_word_backward())
        
    def open_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Pliki tekstowe", "*.txt"), ("Wszystkie pliki", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_area.delete('1.0', tk.END)
                    self.text_area.insert('1.0', content)
                    self.update_line_numbers()
            except Exception as e:
                messagebox.showerror("Błąd", f"Nie można otworzyć pliku: {e}")
                
    def save_file(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Pliki tekstowe", "*.txt"), ("UTF-8", "*.txt")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.text_area.get('1.0', tk.END))
                messagebox.showinfo("Info", "Plik zapisany pomyślnie")
            except Exception as e:
                messagebox.showerror("Błąd", f"Nie można zapisać pliku: {e}")
                
    def execute_command(self, event=None):
        command = self.command_entry.get().strip()
        if not command:
            return
            
        try:
            if command.startswith('/del('):
                self.cmd_delete(command)
            elif command.startswith('/cha('):
                self.cmd_change(command)
            elif command.startswith('/spc('):
                self.cmd_spaces(command)
            elif command.startswith('/aka('):
                self.cmd_indent(command)
            elif command.startswith('/dua('):
                self.cmd_duplicates(command)
            elif command.startswith('/lin('):
                self.cmd_line_color(command)
            elif command.startswith('/lit('):
                self.cmd_text_color(command)
            elif command.startswith('/bol('):
                self.cmd_bold_lines(command)
            else:
                messagebox.showwarning("Błąd", f"Nieznana komenda: {command}")
        except Exception as e:
            messagebox.showerror("Błąd", f"Błąd wykonania komendy: {e}")
            
        self.command_entry.delete(0, tk.END)
        
    def cmd_delete(self, command):
        # /del(0):słowo - usuwa wszystkie wystąpienia słowa
        match = re.match(r'/del\((\d+)\):(.+)', command)
        if match:
            word = match.group(2)
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(word, '')
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_change(self, command):
        # /cha(nowe):stare - zamienia stare na nowe
        match = re.match(r'/cha\(([^)]+)\):(.+)', command)
        if match:
            new_word = match.group(1)
            old_word = match.group(2)
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(old_word, new_word)
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_spaces(self, command):
        # /spc(1):3 - zamienia potrójne spacje na pojedyncze
        match = re.match(r'/spc\((\d+)\):(\d+)', command)
        if match:
            target_spaces = int(match.group(1))
            source_spaces = int(match.group(2))
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(' ' * source_spaces, ' ' * target_spaces)
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_indent(self, command):
        # /aka(4-8):2 - dodaje wcięcia między liniami 4-8
        match = re.match(r'/aka\((\d+)-(\d+)\):(\d+)', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            spaces = int(match.group(3))
            
            lines = self.text_area.get('1.0', tk.END).split('\n')
            for i in range(start_line-1, min(end_line, len(lines))):
                if i < len(lines) and lines[i].strip():
                    lines[i] = ' ' * spaces + lines[i]
                    
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', '\n'.join(lines))
            
    def cmd_duplicates(self, command):
        # /dua(0): - włącza/wyłącza podświetlanie duplikatów
        if '0' in command:
            self.toggle_duplicates()
            
    def cmd_line_color(self, command):
        # /lin(2-5):y - koloruje linie na żółto
        match = re.match(r'/lin\((\d+)-(\d+)\):([yrbgp])', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            color_code = match.group(3)
            
            color_map = {'y': 'yellow', 'r': 'red', 'b': 'blue', 'g': 'green', 'p': 'purple'}
            color = color_map.get(color_code, 'yellow')
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"line_bg_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"line_bg_{line_num}", background=color)
                
    def cmd_text_color(self, command):
        # /lit(3-6):r - koloruje tekst na czerwono
        match = re.match(r'/lit\((\d+)-(\d+)\):([yrbgp])', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            color_code = match.group(3)
            
            color_map = {'y': 'yellow', 'r': 'red', 'b': 'blue', 'g': 'green', 'p': 'purple'}
            color = color_map.get(color_code, 'red')
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"text_color_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"text_color_{line_num}", foreground=color)
                
    def cmd_bold_lines(self, command):
        # /bol(4-7): - pogrubienie linii
        match = re.match(r'/bol\((\d+)-(\d+)\):', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"bold_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"bold_{line_num}", font=(self.font_family, self.font_size, "bold"))
                
    def format_selection(self, format_type):
        try:
            selection = self.text_area.tag_ranges(tk.SEL)
            if selection:
                start, end = selection[0], selection[1]
                
                if format_type == 'bold':
                    self.text_area.tag_add("bold", start, end)
                    self.text_area.tag_config("bold", font=(self.font_family, self.font_size, "bold"))
                elif format_type == 'italic':
                    self.text_area.tag_add("italic", start, end)
                    self.text_area.tag_config("italic", font=(self.font_family, self.font_size, "italic"))
                elif format_type in ['yellow', 'red', 'blue', 'green', 'purple']:
                    self.text_area.tag_add(format_type, start, end)
                    self.text_area.tag_config(format_type, background=format_type)
                elif format_type == 'clear':
                    # Usuwa wszystkie formatowania
                    for tag in ['bold', 'italic', 'yellow', 'red', 'blue', 'green', 'purple']:
                        self.text_area.tag_remove(tag, start, end)
        except tk.TclError:
            pass
            
    def bold_all_text(self):
        self.text_area.tag_add("all_bold", "1.0", tk.END)
        self.text_area.tag_config("all_bold", font=(self.font_family, self.font_size, "bold"))
        
    def jump_to_start(self):
        self.text_area.mark_set(tk.INSERT, "1.0")
        self.text_area.see(tk.INSERT)
        
    def jump_word_forward(self):
        """Skacze między słowami do przodu w tej samej linii"""
        current_pos = self.text_area.index(tk.INSERT)
        current_line = current_pos.split('.')[0]
        line_end = f"{current_line}.end"
        
        # Pobierz tekst od kursora do końca linii
        text_to_end = self.text_area.get(current_pos, line_end)
        
        # Znajdź następne słowo (pomijając spacje na początku)
        match = re.search(r'\S+', text_to_end)
        if match:
            # Przesuń kursor na koniec znalezionego słowa
            word_end_offset = match.end()
            new_pos = self.text_area.index(f"{current_pos}+{word_end_offset}c")
            self.text_area.mark_set(tk.INSERT, new_pos)
            self.text_area.see(tk.INSERT)
            
    def jump_word_backward(self):
        """Skacze między słowami do tyłu w tej samej linii"""
        current_pos = self.text_area.index(tk.INSERT)
        current_line = current_pos.split('.')[0]
        current_col = int(current_pos.split('.')[1])
        line_start = f"{current_line}.0"

        # Pobierz tekst od początku linii do kursora
        text_from_start = self.text_area.get(line_start, current_pos)

        # Jeśli jesteśmy na początku linii, nie rób nic
        if current_col == 0:
            return

        # Znajdź wszystkie słowa w linii
        words = list(re.finditer(r'\S+', text_from_start))

        if words:
            # Sprawdź czy jesteśmy w środku słowa czy na spacji
            current_word = None
            for word in words:
                if word.start() <= current_col - 1 < word.end():
                    current_word = word
                    break

            if current_word and current_col > current_word.start():
                # Jesteśmy w środku słowa - idź na jego początek
                new_pos = self.text_area.index(f"{line_start}+{current_word.start()}c")
            else:
                # Jesteśmy na spacji lub na początku słowa - idź na początek poprzedniego słowa
                prev_words = [w for w in words if w.end() <= current_col]
                if prev_words:
                    prev_word = prev_words[-1]
                    new_pos = self.text_area.index(f"{line_start}+{prev_word.start()}c")
                else:
                    # Przejdź na początek linii
                    new_pos = line_start

            self.text_area.mark_set(tk.INSERT, new_pos)
            self.text_area.see(tk.INSERT)
            
    def on_click(self, event):
        self.update_status()
        
    def on_double_click(self, event):
        # Zaznacza słowo przy podwójnym kliknięciu
        current_pos = self.text_area.index(tk.INSERT)
        word_start = self.text_area.search(r'\b', current_pos, "1.0", backwards=True, regexp=True)
        word_end = self.text_area.search(r'\b', current_pos, tk.END, regexp=True)
        
        if word_start and word_end:
            self.text_area.tag_add(tk.SEL, word_start, word_end)
            
    def change_font_size(self):
        import tkinter.simpledialog
        new_size = tkinter.simpledialog.askinteger("Rozmiar tekstu", "Podaj rozmiar (8-88):",
                                            initialvalue=self.font_size, minvalue=8, maxvalue=88)
        if new_size:
            self.font_size = new_size
            self.text_area.config(font=(self.font_family, self.font_size))
            # Aktualizuj numerację po zmianie rozmiaru
            if self.show_line_numbers:
                self.line_numbers.config(font=(self.font_family, max(8, self.font_size - 2)))
            self.update_line_numbers()
            # Wymuszenie odświeżenia układu
            self.root.update_idletasks()
            
    def change_font_family(self):
        """Zmiana rodziny fontów"""
        font_window = tk.Toplevel(self.root)
        font_window.title("Wybierz Font")
        font_window.geometry("400x500")
        
        # Lista fontów
        listbox = tk.Listbox(font_window, height=20)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Dodaj fonty do listy
        for font in self.available_fonts:
            listbox.insert(tk.END, font)
            
        # Zaznacz aktualny font
        if self.font_family in self.available_fonts:
            index = self.available_fonts.index(self.font_family)
            listbox.selection_set(index)
            listbox.see(index)
        
        def apply_font():
            selection = listbox.curselection()
            if selection:
                selected_font = self.available_fonts[selection[0]]
                self.font_family = selected_font
                self.text_area.config(font=(self.font_family, self.font_size))
                self.update_line_numbers()
                font_window.destroy()
                
        def preview_font(event):
            selection = listbox.curselection()
            if selection:
                selected_font = self.available_fonts[selection[0]]
                try:
                    # Podgląd fontu w etykiecie
                    preview_label.config(font=(selected_font, 14), 
                                       text=f"Podgląd fontu: {selected_font}")
                except:
                    preview_label.config(text=f"Font niedostępny: {selected_font}")
        
        # Podgląd fontu
        preview_label = tk.Label(font_window, text="Podgląd fontu", height=2)
        preview_label.pack(pady=5)
        
        listbox.bind('<<ListboxSelect>>', preview_font)
        
        # Przyciski
        button_frame = tk.Frame(font_window)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Zastosuj", command=apply_font).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Anuluj", command=font_window.destroy).pack(side=tk.LEFT, padx=5)
            
    def change_text_color(self):
        color = colorchooser.askcolor(title="Wybierz kolor tekstu")[1]
        if color:
            self.text_color = color
            self.text_area.config(fg=color)
            
    def change_bg_color(self):
        color = colorchooser.askcolor(title="Wybierz kolor tła")[1]
        if color:
            self.bg_color = color
            self.text_area.config(bg=color)
            
    def change_cursor_color(self):
        """Zmiana koloru kursora"""
        color = colorchooser.askcolor(title="Wybierz kolor kursora")[1]
        if color:
            self.cursor_color = color
            self.text_area.config(insertbackground=color)
            
    def toggle_line_numbers(self):
        self.show_line_numbers = not self.show_line_numbers
        if self.show_line_numbers:
            self.line_frame.grid(row=0, column=0, sticky="ns")
        else:
            self.line_frame.grid_remove()
        self.update_line_numbers()
        
    def toggle_duplicates(self):
        """Przełącza duplikaty z opcją minimalnej długości słów"""
        if not self.show_duplicates:
            # Okno dialogowe dla długości duplikatów
            duplicate_window = tk.Toplevel(self.root)
            duplicate_window.title("Ustawienia duplikatów")
            duplicate_window.geometry("300x150")
            duplicate_window.transient(self.root)
            duplicate_window.grab_set()
            
            tk.Label(duplicate_window, text="Powyżej ilu znaków pokazywać duplikaty?").pack(pady=10)
            
            min_length_var = tk.IntVar(value=3)
            entry = tk.Entry(duplicate_window, textvariable=min_length_var, width=10)
            entry.pack(pady=5)
            entry.focus_set()
            
            def apply_duplicates():
                self.min_duplicate_length = min_length_var.get()
                self.show_duplicates = True
                self.duplicate_button.config(bg='lightgreen')
                self.highlight_duplicates()
                duplicate_window.destroy()
                
            def cancel_duplicates():
                duplicate_window.destroy()
            
            button_frame = tk.Frame(duplicate_window)
            button_frame.pack(pady=10)
            
            tk.Button(button_frame, text="OK", command=apply_duplicates).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Anuluj", command=cancel_duplicates).pack(side=tk.LEFT, padx=5)
            
            entry.bind('<Return>', lambda e: apply_duplicates())
            
        else:
            # Wyłącz duplikaty
            self.show_duplicates = False
            self.duplicate_button.config(bg='SystemButtonFace')
            self.text_area.tag_remove("duplicate", "1.0", tk.END)
            
    def highlight_duplicates(self):
        if not self.show_duplicates:
            return
            
        text = self.text_area.get('1.0', tk.END)
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filtruj słowa według minimalnej długości
        filtered_words = [w for w in words if len(w) >= self.min_duplicate_length]
        word_counts = Counter(filtered_words)
        duplicates = {word for word, count in word_counts.items() if count > 1}
        
        self.text_area.tag_remove("duplicate", "1.0", tk.END)
        
        for word in duplicates:
            start = '1.0'
            while True:
                start = self.text_area.search(word, start, tk.END, nocase=True)
                if not start:
                    break
                end = f"{start}+{len(word)}c"
                self.text_area.tag_add("duplicate", start, end)
                start = end
                
        self.text_area.tag_config("duplicate", background='lightgreen')
        
    def search_text(self, event=None):
        search_term = self.search_entry.get()
        if not search_term:
            return
            
        # Usuń poprzednie zaznaczenia wyszukiwania
        self.text_area.tag_remove("search_highlight", "1.0", tk.END)
        
        text = self.text_area.get('1.0', tk.END)
        count = text.lower().count(search_term.lower())
        
        if count > 0:
            # Zaznacz wszystkie wystąpienia
            start = '1.0'
            while True:
                start = self.text_area.search(search_term, start, tk.END, nocase=True)
                if not start:
                    break
                end = f"{start}+{len(search_term)}c"
                self.text_area.tag_add("search_highlight", start, end)
                start = end
            
            # Konfiguracja podświetlenia wyszukiwania
            self.text_area.tag_config("search_highlight", background='yellow', foreground='red')
            
            # Przejdź do pierwszego wystąpienia
            first_pos = self.text_area.search(search_term, '1.0', tk.END, nocase=True)
            if first_pos:
                self.text_area.mark_set(tk.INSERT, first_pos)
                self.text_area.see(tk.INSERT)
                
        messagebox.showinfo("Wyszukiwanie", f"Znaleziono {count} wystąpień")
        
    def show_report(self):
        text = self.text_area.get('1.0', tk.END)
        words = re.findall(r'\b\w+\b', text.lower())
        lines = text.split('\n')
        
        # Statystyki
        word_counts = Counter(words)
        most_common = word_counts.most_common(1)
        total_chars = len(text)
        total_lines = len(lines)
        spaces = text.count(' ')
        
        # Duplikaty według długości
        three_letter = Counter([w for w in words if len(w) == 3])
        four_letter = Counter([w for w in words if len(w) == 4])
        longer = Counter([w for w in words if len(w) > 4])
        
        # Najdłuższa linia
        longest_line = max(range(len(lines)), key=lambda i: len(lines[i])) + 1 if lines else 0
        
        # Raport
        report = f"""RAPORT STATYSTYCZNY

Najwięcej tych samych słów: {most_common[0] if most_common else 'brak'} 
Najczęstsze słowo 3-literowe: {three_letter.most_common(1)[0] if three_letter else 'brak'}
Najczęstsze słowo 4-literowe: {four_letter.most_common(1)[0] if four_letter else 'brak'}
Najczęstsze słowo dłuższe: {longer.most_common(1)[0] if longer else 'brak'}

Najdłuższa linia: {longest_line}
Wszystkich linii: {total_lines}
Liczba spacji: {spaces}
Wszystkich znaków: {total_chars}

Liczba różnych słów: {len(word_counts)}
"""
        
        # Okno raportu
        report_window = tk.Toplevel(self.root)
        report_window.title("Raport Statystyczny")
        report_window.geometry("500x400")
        
        text_widget = tk.Text(report_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert('1.0', report)
        text_widget.config(state='disabled')
        
        # Przycisk zapisu
        tk.Button(report_window, text="Zapisz raport", 
                 command=lambda: self.save_report(report)).pack(pady=5)
        
    def save_report(self, report):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Pliki tekstowe", "*.txt")]
        )
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(report)
            messagebox.showinfo("Info", "Raport zapisany")
            
    def update_line_numbers(self):
        """Aktualizacja numeracji linii z dopasowaniem do rozmiaru fontu"""
        if not self.show_line_numbers:
            return

        # Ustawienia numeracji - dopasowanie do rozmiaru głównego tekstu
        line_font_size = max(8, self.font_size - 1)  # Podobny rozmiar do głównego tekstu

        # Pobierz liczbę linii z głównego pola tekstowego
        text_content = self.text_area.get('1.0', tk.END)
        line_count = max(1, text_content.count('\n'))

        # Oblicz szerokość pola numeracji na podstawie liczby cyfr
        max_digits = len(str(line_count))
        width = max(4, max_digits + 2)

        self.line_numbers.config(
            font=(self.font_family, line_font_size),
            width=width
        )

        # Wyczyść i wygeneruj nową numerację
        self.line_numbers.delete('1.0', tk.END)

        # Generowanie numerów linii z odpowiednim wyrównaniem
        line_numbers_text = []
        for i in range(1, line_count + 1):
            line_numbers_text.append(f"{i:>{max_digits}}")

        if line_numbers_text:
            self.line_numbers.insert('1.0', '\n'.join(line_numbers_text))

        # Synchronizacja wysokości linii z głównym polem tekstowym
        self.line_numbers.config(height=self.text_area.cget('height'))

        # Aktualizacja przewijania
        self.line_numbers.yview_moveto(self.text_area.yview()[0])
        
    def update_status(self, event=None):
        # Pozycja kursora
        cursor_pos = self.text_area.index(tk.INSERT)
        line, column = cursor_pos.split('.')

        # Liczba znaków
        total_chars = len(self.text_area.get('1.0', tk.END)) - 1  # -1 dla końcowego \n

        # Zaznaczenie
        try:
            selection = self.text_area.get(tk.SEL_FIRST, tk.SEL_LAST)
            selected_chars = len(selection)
        except tk.TclError:
            selected_chars = 0

        self.status_bar.config(
            text=f"Znaków: {total_chars} | Linia: {line} | Kolumna: {int(column)+1} | Zaznaczenie: {selected_chars}"
        )

        # Aktualizacja numeracji i duplikatów z opóźnieniem
        if self.show_line_numbers:
            self.root.after_idle(self.update_line_numbers)
        if self.show_duplicates:
            self.root.after(200, self.highlight_duplicates)  # Zwiększone opóźnienie dla lepszej wydajności
            
    def toggle_command_bar(self):
        """Funkcja zachowana dla kompatybilności - paski są teraz zawsze widoczne"""
        messagebox.showinfo("Info", "Paski komend i przycisków są teraz zawsze widoczne")
            
    def show_help(self):
        help_text = """INSTRUKCJA TEKSTDYRYGENT

SKRÓTY KLAWISZOWE:
Ctrl+F - pogrub zaznaczenie
Ctrl+I - kursywa
Ctrl+Y/R/B/G/P - kolory (żółty/czerwony/niebieski/zielony/fioletowy)
Ctrl+0 - usuń formatowanie
Ctrl+Alt+F - pogrub cały tekst
Ctrl+Q - przejdź na początek
Ctrl+W/S - skacz między słowami

KOMENDY:
/del(0):słowo - usuń słowo
/cha(nowe):stare - zamień stare na nowe
/spc(1):3 - zamień potrójne spacje na pojedyncze
/aka(4-8):2 - dodaj wcięcia
/dua(0): - przełącz duplikaty
/lin(2-5):y - koloruj linie
/lit(3-6):r - koloruj tekst
/bol(4-7): - pogrub linie

NOWE FUNKCJE:
- Przycisk "Font" - wybór fontów systemowych i z folderu "fonts"
- Przycisk "Kolor kursora" - zmiana koloru kursora
- Numeracja automatycznie dostosowuje się do rozmiaru tekstu
"""
        messagebox.showinfo("Instrukcja", help_text)
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TekstDyrygent()
    app.run()
