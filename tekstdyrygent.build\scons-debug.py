# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe', '-W', 'ignore', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '16', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.10', 'python_prefix=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'console_mode=disable', 'noelf_mode=true', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=2', 'result_exe=C:\\Users\\<USER>\\Desktop\\BEZPIE~1\\TEKSTD~1\\TEKSTD~1.DIS\\tekstdyrygent.dll', 'frozen_modules=152', 'python_sysflag_no_site=true'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','CHROME_CRASHPAD_PIPE_NAME': '\\\\.\\pipe\\crashpad_18260_MJWMLWKKXSRXFPCJ','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': 'ENDORFINA','COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','EFC_15588_1262719628': '1','EFC_15588_1592913036': '1','EFC_15588_2283032206': '1','EFC_15588_2775293581': '1','EFC_15588_3789132940': '1','FPS_BROWSER_APP_PROFILE_STRING': 'Internet Explorer','FPS_BROWSER_USER_PROFILE_STRING': 'Default','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\Endorfinka','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\ENDORFINA','NUMBER_OF_PROCESSORS': '16','OLLAMA_HOME': 'C:\\Users\\<USER>\\.ollama\\models\\codelama','ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive','ONEDRIVECONSUMER': 'C:\\Users\\<USER>\\OneDrive','ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined','OS': 'Windows_NT','PATH': ';C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\nodejs\\;C:\\Program Files\\Graphviz\\bin;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Muse Hub\\lib;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 191 Stepping 2, GenuineIntel','PROCESSOR_LEVEL': '6','PROCESSOR_REVISION': 'bf02','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PROMPT': '$P$G','PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules','PUBLIC': 'C:\\Users\\<USER>\\WINDOWS','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','USERDOMAIN': 'ENDORFINA','USERDOMAIN_ROAMINGPROFILE': 'ENDORFINA','USERNAME': 'Endorfinka','USERPROFILE': 'C:\\Users\\<USER>\\WINDOWS','__PSLOCKDOWNPOLICY': '0','LANG': 'en_US.UTF-8','CLAUDE_CODE_SSE_PORT': '22986','ENABLE_IDE_INTEGRATION': 'true','PYDEVD_DISABLE_FILE_VALIDATION': '1','BUNDLED_DEBUGPY_PATH': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\libs\\debugpy','NUITKA_PYTHON_EXE_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','NUITKA_COMPANY_NAME': 'TekstDyrygent','NUITKA_PRODUCT_NAME': 'TekstDyrygent - Zaawansowany Notatnik','NUITKA_VERSION_COMBINED': '1.0.0.0','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': 'NUITKA_COMPANY_NAME,NUITKA_PRODUCT_NAME,NUITKA_VERSION_COMBINED,_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)