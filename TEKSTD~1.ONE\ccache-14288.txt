[2025-07-28T20:09:13.851084 21984] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T20:09:13.851170 21984] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T20:09:13.851212 21984] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T20:09:13.851212 21984] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T20:09:13.851212 21984] Config: (default) base_dir = 
[2025-07-28T20:09:13.851212 21984] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T20:09:13.851212 21984] Config: (default) compiler = 
[2025-07-28T20:09:13.851212 21984] Config: (default) compiler_check = mtime
[2025-07-28T20:09:13.851212 21984] Config: (default) compiler_type = auto
[2025-07-28T20:09:13.851212 21984] Config: (default) compression = true
[2025-07-28T20:09:13.851212 21984] Config: (default) compression_level = 0
[2025-07-28T20:09:13.851212 21984] Config: (default) cpp_extension = 
[2025-07-28T20:09:13.851212 21984] Config: (default) debug = false
[2025-07-28T20:09:13.851212 21984] Config: (default) debug_dir = 
[2025-07-28T20:09:13.851212 21984] Config: (default) debug_level = 2
[2025-07-28T20:09:13.851212 21984] Config: (default) depend_mode = false
[2025-07-28T20:09:13.851212 21984] Config: (default) direct_mode = true
[2025-07-28T20:09:13.851212 21984] Config: (default) disable = false
[2025-07-28T20:09:13.851212 21984] Config: (default) extra_files_to_hash = 
[2025-07-28T20:09:13.851212 21984] Config: (default) file_clone = false
[2025-07-28T20:09:13.851212 21984] Config: (default) hard_link = false
[2025-07-28T20:09:13.851212 21984] Config: (default) hash_dir = true
[2025-07-28T20:09:13.851212 21984] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T20:09:13.851212 21984] Config: (default) ignore_options = 
[2025-07-28T20:09:13.851212 21984] Config: (default) inode_cache = false
[2025-07-28T20:09:13.851212 21984] Config: (default) keep_comments_cpp = false
[2025-07-28T20:09:13.851212 21984] Config: (environment) log_file = C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.ONE\ccache-14288.txt
[2025-07-28T20:09:13.851212 21984] Config: (default) max_files = 0
[2025-07-28T20:09:13.851212 21984] Config: (default) max_size = 5.0 GiB
[2025-07-28T20:09:13.851212 21984] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T20:09:13.851212 21984] Config: (default) namespace = 
[2025-07-28T20:09:13.851212 21984] Config: (default) path = 
[2025-07-28T20:09:13.851212 21984] Config: (default) pch_external_checksum = false
[2025-07-28T20:09:13.851212 21984] Config: (default) prefix_command = 
[2025-07-28T20:09:13.851212 21984] Config: (default) prefix_command_cpp = 
[2025-07-28T20:09:13.851212 21984] Config: (default) read_only = false
[2025-07-28T20:09:13.851212 21984] Config: (default) read_only_direct = false
[2025-07-28T20:09:13.851212 21984] Config: (default) recache = false
[2025-07-28T20:09:13.851212 21984] Config: (default) remote_only = false
[2025-07-28T20:09:13.851212 21984] Config: (default) remote_storage = 
[2025-07-28T20:09:13.851212 21984] Config: (default) reshare = false
[2025-07-28T20:09:13.851212 21984] Config: (default) run_second_cpp = true
[2025-07-28T20:09:13.851212 21984] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T20:09:13.851212 21984] Config: (default) stats = true
[2025-07-28T20:09:13.851212 21984] Config: (default) stats_log = 
[2025-07-28T20:09:13.851212 21984] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T20:09:13.851212 21984] Config: (default) umask = 
[2025-07-28T20:09:13.851290 21984] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_EXE_MODE -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T20:09:13.854555 21984] Hostname: Endorfina
[2025-07-28T20:09:13.854580 21984] Working directory: C:\Users\<USER>\Desktop\BEZPIE~1\TEKSTD~1\TEKSTD~1.ONE
[2025-07-28T20:09:13.854606 21984] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T20:09:13.854612 21984] Compiler type: gcc
[2025-07-28T20:09:13.854845 21984] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T20:09:13.854973 21984] Source file: static_src\OnefileBootstrap.c
[2025-07-28T20:09:13.854980 21984] Object file: static_src\OnefileBootstrap.o
[2025-07-28T20:09:13.855147 21984] Trying direct lookup
[2025-07-28T20:09:13.855416 21984] Manifest key: 71079h1ht6n9gn3svhmpmg3vhr19aq0jc
[2025-07-28T20:09:13.862416 21984] Retrieved 71079h1ht6n9gn3svhmpmg3vhr19aq0jc from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/1/079h1ht6n9gn3svhmpmg3vhr19aq0jcM)
[2025-07-28T20:09:13.903617 21984] Got result key from manifest
[2025-07-28T20:09:13.903647 21984] Result key: 4d11h4i9la6blp493rkmvn1d6b8em5s1m
[2025-07-28T20:09:13.911132 21984] Retrieved 4d11h4i9la6blp493rkmvn1d6b8em5s1m from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/d/11h4i9la6blp493rkmvn1d6b8em5s1mR)
[2025-07-28T20:09:13.911756 21984] Reading embedded entry #0 .o (616387 bytes)
[2025-07-28T20:09:13.911778 21984] Writing to static_src\OnefileBootstrap.o
[2025-07-28T20:09:13.912365 21984] Succeeded getting cached result
[2025-07-28T20:09:13.912484 21984] Result: direct_cache_hit
[2025-07-28T20:09:13.912492 21984] Result: local_storage_hit
[2025-07-28T20:09:13.912497 21984] Result: local_storage_read_hit
[2025-07-28T20:09:13.912502 21984] Result: local_storage_read_hit
[2025-07-28T20:09:13.912524 21984] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/0/stats.lock
[2025-07-28T20:09:13.913446 21984] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/0/stats.lock
[2025-07-28T20:09:13.920363 21984] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/0/stats.lock
[2025-07-28T20:09:13.920454 21984] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/0/stats.lock
