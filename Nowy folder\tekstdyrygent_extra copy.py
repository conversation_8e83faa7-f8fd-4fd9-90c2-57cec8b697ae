import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import re
from collections import Counter

class TekstDyrygent:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TekstDyrygent - Zaawansowany Notatnik")
        self.root.geometry("1000x700")
        
        # Domyślne ustawienia
        self.font_size = 12
        self.font_family = "Arial"
        self.text_color = "black"
        self.bg_color = "white"
        self.cursor_color = "black"
        self.show_line_numbers = False
        self.show_duplicates = False
        self.min_duplicate_length = 3  # Minimalna długość duplikatów
        self.available_fonts = self.get_available_fonts()
        
        self.setup_ui()
        self.bind_shortcuts()
        
    def get_available_fonts(self):
        """Pobiera dostępne fonty systemowe i z foldera fonts (jeśli istnieje)"""
        import tkinter.font as tkFont
        import os
        
        # Fonty systemowe
        system_fonts = list(tkFont.families())
        
        # Pr<PERSON><PERSON> załadowania fontów z foldera fonts
        fonts_folder = os.path.join(os.getcwd(), "fonts")
        custom_fonts = []
        
        if os.path.exists(fonts_folder):
            for file in os.listdir(fonts_folder):
                if file.lower().endswith(('.ttf', '.otf')):
                    font_name = os.path.splitext(file)[0]
                    custom_fonts.append(font_name)
        
        # Połącz wszystkie fonty i usuń duplikaty
        all_fonts = sorted(list(set(system_fonts + custom_fonts)))
        return all_fonts
        
    def setup_ui(self):
        # Menu
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Pliki
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Pliki", menu=file_menu)
        file_menu.add_command(label="Otwórz", command=self.open_file)
        file_menu.add_command(label="Zapisz", command=self.save_file)
        file_menu.add_command(label="Szybki zapis (F5)", command=self.quick_save)
        file_menu.add_separator()
        file_menu.add_command(label="Zakończ", command=self.exit_app)

        # Menu Edycja
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edycja", menu=edit_menu)
        edit_menu.add_command(label="Cofnij", command=self.undo_action)
        edit_menu.add_command(label="Ponów", command=self.redo_action)

        # Menu Info
        info_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Info", menu=info_menu)
        info_menu.add_command(label="Instrukcja", command=self.show_help)

        # Główny kontener z układem grid dla lepszej kontroli
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        main_container = tk.Frame(self.root)
        main_container.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)

        # Frame dla tekstu - główny obszar
        text_frame = tk.Frame(main_container)
        text_frame.grid(row=0, column=0, sticky="nsew", pady=(0,5))
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        # Główne pole tekstowe z obsługą undo/redo (max 3 kroki)
        self.text_area = tk.Text(text_frame, wrap=tk.WORD, undo=True, maxundo=3,
                                font=(self.font_family, self.font_size),
                                fg=self.text_color, bg=self.bg_color,
                                insertbackground=self.cursor_color)
        self.text_area.grid(row=0, column=0, sticky="nsew")

        # Scrollbar
        self.scrollbar = tk.Scrollbar(text_frame)
        self.scrollbar.grid(row=0, column=1, sticky="ns")
        self.text_area.config(yscrollcommand=self.scrollbar.set)
        self.scrollbar.config(command=self.text_area.yview)

        # Pasek przycisków - zawsze widoczny na dole
        button_frame = tk.Frame(main_container, relief=tk.RAISED, bd=1)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(0,2))

        # Przyciski
        tk.Button(button_frame, text="Rozmiar tekstu", command=self.change_font_size).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Font", command=self.change_font_family).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor tekstu", command=self.change_text_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor tła", command=self.change_bg_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Kolor kursora", command=self.change_cursor_color).pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="Numeracja", command=self.toggle_line_numbers).pack(side=tk.LEFT, padx=2)

        self.duplicate_button = tk.Button(button_frame, text="Duplikaty", command=self.toggle_duplicates)
        self.duplicate_button.pack(side=tk.LEFT, padx=2)

        tk.Button(button_frame, text="Raport", command=self.show_report).pack(side=tk.LEFT, padx=2)

        # Pole wyszukiwania
        tk.Label(button_frame, text="Znajdź:").pack(side=tk.LEFT, padx=(10,2))
        self.search_entry = tk.Entry(button_frame, width=20)
        self.search_entry.pack(side=tk.LEFT, padx=2)
        self.search_entry.bind('<Return>', self.search_text)

        # Pasek komend - zawsze widoczny
        self.command_frame = tk.Frame(main_container, relief=tk.RAISED, bd=1)
        self.command_frame.grid(row=2, column=0, sticky="ew", pady=(0,2))

        tk.Label(self.command_frame, text="Komenda:").pack(side=tk.LEFT, padx=(5,0))
        self.command_entry = tk.Entry(self.command_frame)
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5,5))
        self.command_entry.bind('<Return>', self.execute_command)

        # Pasek statusu - zawsze widoczny na samym dole
        self.status_bar = tk.Label(main_container, text="Znaków: 0 | Linia: 1 | Kolumna: 1 | Zaznaczenie: 0",
                                  relief=tk.SUNKEN, anchor=tk.W, bd=1)
        self.status_bar.grid(row=3, column=0, sticky="ew")

        # Bindy dla aktualizacji statusu
        self.text_area.bind('<KeyRelease>', self.update_status)
        self.text_area.bind('<ButtonRelease>', self.update_status)
        self.text_area.bind('<Motion>', self.update_status)
        self.text_area.bind('<Button-1>', self.on_click)
        self.text_area.bind('<Double-Button-1>', self.on_double_click)

        # Bind dla zmiany rozmiaru okna
        self.root.bind('<Configure>', self.on_window_configure)

    def on_window_configure(self, event):
        """Obsługa zmiany rozmiaru okna - zapewnia widoczność pasków"""
        if event.widget == self.root:
            # Wymuszenie aktualizacji układu
            self.root.update_idletasks()

    def bind_shortcuts(self):
        # Skróty klawiszowe
        self.root.bind('<Control-f>', lambda e: self.format_selection('bold'))
        self.root.bind('<Control-i>', lambda e: self.format_selection('italic'))
        self.root.bind('<Control-y>', lambda e: self.format_selection('yellow'))
        self.root.bind('<Control-r>', lambda e: self.format_selection('red'))
        self.root.bind('<Control-b>', lambda e: self.format_selection('blue'))
        self.root.bind('<Control-g>', lambda e: self.format_selection('green'))
        self.root.bind('<Control-p>', lambda e: self.format_selection('purple'))
        self.root.bind('<Control-0>', lambda e: self.format_selection('clear'))

        self.root.bind('<Control-Alt-f>', lambda e: self.bold_all_text())
        self.root.bind('<Control-q>', lambda e: self.jump_to_start())
        self.root.bind('<Control-w>', lambda e: self.jump_word_forward())
        self.root.bind('<Control-s>', lambda e: self.jump_word_backward())

        # Nowe skróty
        self.root.bind('<F5>', lambda e: self.quick_save())
        self.root.bind('<Control-z>', lambda e: self.undo_action())
        self.root.bind('<Control-Shift-z>', lambda e: self.redo_action())  # Ctrl+Shift+Z dla redo
        
    def open_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Pliki tekstowe", "*.txt"), ("Wszystkie pliki", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_area.delete('1.0', tk.END)
                    self.text_area.insert('1.0', content)
            except Exception as e:
                messagebox.showerror("Błąd", f"Nie można otworzyć pliku: {e}")
                
    def save_file(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Pliki tekstowe", "*.txt"), ("UTF-8", "*.txt")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.text_area.get('1.0', tk.END))
                messagebox.showinfo("Info", "Plik zapisany pomyślnie")
            except Exception as e:
                messagebox.showerror("Błąd", f"Nie można zapisać pliku: {e}")

    def quick_save(self):
        """Szybki zapis - F5"""
        # Sprawdź czy jest jakiś tekst
        text_content = self.text_area.get('1.0', tk.END).strip()
        if not text_content:
            messagebox.showwarning("Uwaga", "Brak tekstu do zapisania!")
            return

        # Automatyczna nazwa pliku z datą i czasem
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"tekst_{timestamp}.txt"

        try:
            with open(default_filename, 'w', encoding='utf-8') as file:
                file.write(self.text_area.get('1.0', tk.END))
            messagebox.showinfo("Szybki zapis", f"Plik zapisany jako: {default_filename}")
        except Exception as e:
            messagebox.showerror("Błąd", f"Nie można zapisać pliku: {e}")

    def exit_app(self):
        """Zakończ aplikację z potwierdzeniem"""
        # Sprawdź czy są niezapisane zmiany
        text_content = self.text_area.get('1.0', tk.END).strip()
        if text_content:
            result = messagebox.askyesnocancel(
                "Zakończ",
                "Czy chcesz zapisać zmiany przed zakończeniem?\n\n"
                "Tak - zapisz i zakończ\n"
                "Nie - zakończ bez zapisywania\n"
                "Anuluj - powrót do edycji"
            )
            if result is True:  # Tak - zapisz
                self.save_file()
                self.root.quit()
            elif result is False:  # Nie - nie zapisuj
                self.root.quit()
            # None - Anuluj, nie rób nic
        else:
            self.root.quit()

    def undo_action(self):
        """Cofnij ostatnią akcję (do 3 kroków)"""
        try:
            self.text_area.edit_undo()
        except tk.TclError:
            messagebox.showinfo("Cofnij", "Brak akcji do cofnięcia")

    def redo_action(self):
        """Ponów cofniętą akcję"""
        try:
            self.text_area.edit_redo()
        except tk.TclError:
            messagebox.showinfo("Ponów", "Brak akcji do ponowienia")

    def execute_command(self, event=None):
        command = self.command_entry.get().strip()
        if not command:
            return
            
        try:
            if command.startswith('/del('):
                self.cmd_delete(command)
            elif command.startswith('/cha('):
                self.cmd_change(command)
            elif command.startswith('/spc('):
                self.cmd_spaces(command)
            elif command.startswith('/aka('):
                self.cmd_indent(command)
            elif command.startswith('/dua('):
                self.cmd_duplicates(command)
            elif command.startswith('/lin('):
                self.cmd_line_color(command)
            elif command.startswith('/lit('):
                self.cmd_text_color(command)
            elif command.startswith('/bol('):
                self.cmd_bold_lines(command)
            else:
                messagebox.showwarning("Błąd", f"Nieznana komenda: {command}")
        except Exception as e:
            messagebox.showerror("Błąd", f"Błąd wykonania komendy: {e}")
            
        self.command_entry.delete(0, tk.END)
        
    def cmd_delete(self, command):
        # /del(0):słowo - usuwa wszystkie wystąpienia słowa
        match = re.match(r'/del\((\d+)\):(.+)', command)
        if match:
            word = match.group(2)
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(word, '')
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_change(self, command):
        # /cha(nowe):stare - zamienia stare na nowe
        match = re.match(r'/cha\(([^)]+)\):(.+)', command)
        if match:
            new_word = match.group(1)
            old_word = match.group(2)
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(old_word, new_word)
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_spaces(self, command):
        # /spc(1):3 - zamienia potrójne spacje na pojedyncze
        match = re.match(r'/spc\((\d+)\):(\d+)', command)
        if match:
            target_spaces = int(match.group(1))
            source_spaces = int(match.group(2))
            text = self.text_area.get('1.0', tk.END)
            new_text = text.replace(' ' * source_spaces, ' ' * target_spaces)
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', new_text)
            
    def cmd_indent(self, command):
        # /aka(4-8):2 - dodaje wcięcia między liniami 4-8
        match = re.match(r'/aka\((\d+)-(\d+)\):(\d+)', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            spaces = int(match.group(3))
            
            lines = self.text_area.get('1.0', tk.END).split('\n')
            for i in range(start_line-1, min(end_line, len(lines))):
                if i < len(lines) and lines[i].strip():
                    lines[i] = ' ' * spaces + lines[i]
                    
            self.text_area.delete('1.0', tk.END)
            self.text_area.insert('1.0', '\n'.join(lines))
            
    def cmd_duplicates(self, command):
        # /dua(0): - włącza/wyłącza podświetlanie duplikatów
        if '0' in command:
            self.toggle_duplicates()
            
    def cmd_line_color(self, command):
        # /lin(2-5):y - koloruje linie na żółto
        match = re.match(r'/lin\((\d+)-(\d+)\):([yrbgp])', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            color_code = match.group(3)
            
            color_map = {'y': 'yellow', 'r': 'red', 'b': 'blue', 'g': 'green', 'p': 'purple'}
            color = color_map.get(color_code, 'yellow')
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"line_bg_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"line_bg_{line_num}", background=color)
                
    def cmd_text_color(self, command):
        # /lit(3-6):r - koloruje tekst na czerwono
        match = re.match(r'/lit\((\d+)-(\d+)\):([yrbgp])', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            color_code = match.group(3)
            
            color_map = {'y': 'yellow', 'r': 'red', 'b': 'blue', 'g': 'green', 'p': 'purple'}
            color = color_map.get(color_code, 'red')
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"text_color_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"text_color_{line_num}", foreground=color)
                
    def cmd_bold_lines(self, command):
        # /bol(4-7): - pogrubienie linii
        match = re.match(r'/bol\((\d+)-(\d+)\):', command)
        if match:
            start_line = int(match.group(1))
            end_line = int(match.group(2))
            
            for line_num in range(start_line, end_line + 1):
                start_pos = f"{line_num}.0"
                end_pos = f"{line_num}.end"
                self.text_area.tag_add(f"bold_{line_num}", start_pos, end_pos)
                self.text_area.tag_config(f"bold_{line_num}", font=(self.font_family, self.font_size, "bold"))
                
    def format_selection(self, format_type):
        try:
            selection = self.text_area.tag_ranges(tk.SEL)
            if selection:
                start, end = selection[0], selection[1]
                
                if format_type == 'bold':
                    self.text_area.tag_add("bold", start, end)
                    self.text_area.tag_config("bold", font=(self.font_family, self.font_size, "bold"))
                elif format_type == 'italic':
                    self.text_area.tag_add("italic", start, end)
                    self.text_area.tag_config("italic", font=(self.font_family, self.font_size, "italic"))
                elif format_type in ['yellow', 'red', 'blue', 'green', 'purple']:
                    self.text_area.tag_add(format_type, start, end)
                    self.text_area.tag_config(format_type, background=format_type)
                elif format_type == 'clear':
                    # Usuwa wszystkie formatowania
                    for tag in ['bold', 'italic', 'yellow', 'red', 'blue', 'green', 'purple']:
                        self.text_area.tag_remove(tag, start, end)
        except tk.TclError:
            pass
            
    def bold_all_text(self):
        self.text_area.tag_add("all_bold", "1.0", tk.END)
        self.text_area.tag_config("all_bold", font=(self.font_family, self.font_size, "bold"))
        
    def jump_to_start(self):
        self.text_area.mark_set(tk.INSERT, "1.0")
        self.text_area.see(tk.INSERT)
        
    def jump_word_forward(self):
        """Skacze między słowami do przodu w tej samej linii"""
        current_pos = self.text_area.index(tk.INSERT)
        current_line = current_pos.split('.')[0]
        line_end = f"{current_line}.end"
        
        # Pobierz tekst od kursora do końca linii
        text_to_end = self.text_area.get(current_pos, line_end)
        
        # Znajdź następne słowo (pomijając spacje na początku)
        match = re.search(r'\S+', text_to_end)
        if match:
            # Przesuń kursor na koniec znalezionego słowa
            word_end_offset = match.end()
            new_pos = self.text_area.index(f"{current_pos}+{word_end_offset}c")
            self.text_area.mark_set(tk.INSERT, new_pos)
            self.text_area.see(tk.INSERT)
            
    def jump_word_backward(self):
        """Skacze między słowami do tyłu w tej samej linii"""
        current_pos = self.text_area.index(tk.INSERT)
        current_line = current_pos.split('.')[0]
        current_col = int(current_pos.split('.')[1])
        line_start = f"{current_line}.0"

        # Pobierz tekst od początku linii do kursora
        text_from_start = self.text_area.get(line_start, current_pos)

        # Jeśli jesteśmy na początku linii, nie rób nic
        if current_col == 0:
            return

        # Znajdź wszystkie słowa w linii
        words = list(re.finditer(r'\S+', text_from_start))

        if words:
            # Sprawdź czy jesteśmy w środku słowa czy na spacji
            current_word = None
            for word in words:
                if word.start() <= current_col - 1 < word.end():
                    current_word = word
                    break

            if current_word and current_col > current_word.start():
                # Jesteśmy w środku słowa - idź na jego początek
                new_pos = self.text_area.index(f"{line_start}+{current_word.start()}c")
            else:
                # Jesteśmy na spacji lub na początku słowa - idź na początek poprzedniego słowa
                prev_words = [w for w in words if w.end() <= current_col]
                if prev_words:
                    prev_word = prev_words[-1]
                    new_pos = self.text_area.index(f"{line_start}+{prev_word.start()}c")
                else:
                    # Przejdź na początek linii
                    new_pos = line_start

            self.text_area.mark_set(tk.INSERT, new_pos)
            self.text_area.see(tk.INSERT)
            
    def on_click(self, event):
        self.update_status()
        
    def on_double_click(self, event):
        # Zaznacza słowo przy podwójnym kliknięciu
        current_pos = self.text_area.index(tk.INSERT)
        word_start = self.text_area.search(r'\b', current_pos, "1.0", backwards=True, regexp=True)
        word_end = self.text_area.search(r'\b', current_pos, tk.END, regexp=True)
        
        if word_start and word_end:
            self.text_area.tag_add(tk.SEL, word_start, word_end)
            
    def change_font_size(self):
        import tkinter.simpledialog
        new_size = tkinter.simpledialog.askinteger("Rozmiar tekstu", "Podaj rozmiar (8-88):",
                                            initialvalue=self.font_size, minvalue=8, maxvalue=88)
        if new_size:
            self.font_size = new_size
            self.text_area.config(font=(self.font_family, self.font_size))
            # Wymuszenie odświeżenia układu
            self.root.update_idletasks()
            
    def change_font_family(self):
        """Zmiana rodziny fontów"""
        font_window = tk.Toplevel(self.root)
        font_window.title("Wybierz Font")
        font_window.geometry("400x500")
        
        # Lista fontów
        listbox = tk.Listbox(font_window, height=20)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Dodaj fonty do listy
        for font in self.available_fonts:
            listbox.insert(tk.END, font)
            
        # Zaznacz aktualny font
        if self.font_family in self.available_fonts:
            index = self.available_fonts.index(self.font_family)
            listbox.selection_set(index)
            listbox.see(index)
        
        def apply_font():
            selection = listbox.curselection()
            if selection:
                selected_font = self.available_fonts[selection[0]]
                self.font_family = selected_font
                self.text_area.config(font=(self.font_family, self.font_size))
                font_window.destroy()
                
        def preview_font(event):
            selection = listbox.curselection()
            if selection:
                selected_font = self.available_fonts[selection[0]]
                try:
                    # Podgląd fontu w etykiecie
                    preview_label.config(font=(selected_font, 14), 
                                       text=f"Podgląd fontu: {selected_font}")
                except:
                    preview_label.config(text=f"Font niedostępny: {selected_font}")
        
        # Podgląd fontu
        preview_label = tk.Label(font_window, text="Podgląd fontu", height=2)
        preview_label.pack(pady=5)
        
        listbox.bind('<<ListboxSelect>>', preview_font)
        
        # Przyciski
        button_frame = tk.Frame(font_window)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Zastosuj", command=apply_font).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Anuluj", command=font_window.destroy).pack(side=tk.LEFT, padx=5)
            
    def change_text_color(self):
        color = colorchooser.askcolor(title="Wybierz kolor tekstu")[1]
        if color:
            self.text_color = color
            self.text_area.config(fg=color)
            
    def change_bg_color(self):
        color = colorchooser.askcolor(title="Wybierz kolor tła")[1]
        if color:
            self.bg_color = color
            self.text_area.config(bg=color)
            
    def change_cursor_color(self):
        """Zmiana koloru kursora"""
        color = colorchooser.askcolor(title="Wybierz kolor kursora")[1]
        if color:
            self.cursor_color = color
            self.text_area.config(insertbackground=color)
            
    def toggle_line_numbers(self):
        """Numerowanie linii tekstu - uproszczona wersja z lepszą obsługą błędów"""
        # Sprawdź czy jest jakiś tekst
        text = self.text_area.get('1.0', tk.END).strip()
        if not text:
            messagebox.showwarning("Uwaga", "Brak tekstu do numerowania!")
            return

        # Okno dialogowe dla ustawień numeracji
        numbering_window = tk.Toplevel(self.root)
        numbering_window.title("Numeracja linii")
        numbering_window.geometry("450x280")
        numbering_window.transient(self.root)
        numbering_window.grab_set()

        # Policz linie w tekście
        lines = text.split('\n')
        total_lines = len(lines)

        # Informacja o liczbie linii
        info_label = tk.Label(numbering_window, text=f"Tekst ma {total_lines} linii",
                             font=("Arial", 10, "bold"), fg="blue")
        info_label.grid(row=0, column=0, columnspan=2, pady=5)

        # Linia początkowa
        tk.Label(numbering_window, text="Od linii:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        start_line_var = tk.IntVar(value=1)
        start_line_entry = tk.Entry(numbering_window, textvariable=start_line_var, width=15)
        start_line_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # Linia końcowa
        tk.Label(numbering_window, text="Do linii:").grid(row=2, column=0, padx=10, pady=5, sticky="w")
        end_line_var = tk.IntVar(value=min(5, total_lines))
        end_line_entry = tk.Entry(numbering_window, textvariable=end_line_var, width=15)
        end_line_entry.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        # Liczba początkowa numeracji
        tk.Label(numbering_window, text="Numer początkowy:").grid(row=3, column=0, padx=10, pady=5, sticky="w")
        start_number_var = tk.IntVar(value=1)
        start_number_entry = tk.Entry(numbering_window, textvariable=start_number_var, width=15)
        start_number_entry.grid(row=3, column=1, padx=10, pady=5, sticky="w")

        # Znak po numerze
        tk.Label(numbering_window, text="Znak po numerze:").grid(row=4, column=0, padx=10, pady=5, sticky="w")
        separator_var = tk.StringVar(value=".")
        separator_entry = tk.Entry(numbering_window, textvariable=separator_var, width=15)
        separator_entry.grid(row=4, column=1, padx=10, pady=5, sticky="w")

        # Spacje po znaku
        tk.Label(numbering_window, text="Spacje po znaku:").grid(row=5, column=0, padx=10, pady=5, sticky="w")
        spacing_var = tk.IntVar(value=4)
        spacing_entry = tk.Entry(numbering_window, textvariable=spacing_var, width=15)
        spacing_entry.grid(row=5, column=1, padx=10, pady=5, sticky="w")

        # Przykład dynamiczny
        example_label = tk.Label(numbering_window, text="", fg="green", font=("Arial", 9))
        example_label.grid(row=6, column=0, columnspan=2, pady=5)

        def update_example():
            """Aktualizuje przykład na żywo"""
            try:
                num = start_number_var.get()
                sep = separator_var.get() or "."
                spaces = " " * spacing_var.get()
                example_text = f"Przykład: '{num}{sep}{spaces}tekst linii'"
                example_label.config(text=example_text)
            except:
                example_label.config(text="Przykład: '1.    tekst linii'")

        # Bindy do aktualizacji przykładu
        for var in [start_number_var, separator_var, spacing_var]:
            if hasattr(var, 'trace'):
                var.trace('w', lambda *args: update_example())

        update_example()  # Początkowy przykład

        def apply_numbering():
            try:
                # Pobierz wartości z bezpieczną obsługą błędów
                start_line = start_line_var.get()
                end_line = end_line_var.get()
                start_num = start_number_var.get()
                separator = separator_var.get().strip()
                spacing = spacing_var.get()

                # Walidacja danych
                if start_line < 1:
                    messagebox.showerror("Błąd", "Linia początkowa musi być większa od 0!")
                    return

                if end_line > total_lines:
                    messagebox.showerror("Błąd", f"Linia końcowa nie może być większa od {total_lines}!")
                    return

                if start_line > end_line:
                    messagebox.showerror("Błąd", "Linia początkowa nie może być większa od końcowej!")
                    return

                if spacing < 0 or spacing > 20:
                    messagebox.showerror("Błąd", "Liczba spacji musi być między 0 a 20!")
                    return

                if not separator:
                    separator = "."

                # Pobierz tekst i podziel na linie
                current_text = self.text_area.get('1.0', tk.END)
                lines = current_text.split('\n')

                # Usuń ostatnią pustą linię jeśli istnieje
                if lines and lines[-1] == '':
                    lines = lines[:-1]

                # Dodaj numerację
                current_number = start_num
                spacing_text = ' ' * spacing

                for i in range(start_line - 1, end_line):  # -1 bo indeksy od 0
                    if i < len(lines):
                        original_line = lines[i]
                        lines[i] = f"{current_number}{separator}{spacing_text}{original_line}"
                        current_number += 1

                # Zastąp tekst
                self.text_area.delete('1.0', tk.END)
                self.text_area.insert('1.0', '\n'.join(lines))

                messagebox.showinfo("Sukces", f"Numeracja zastosowana do linii {start_line}-{end_line}")
                numbering_window.destroy()

            except tk.TclError as e:
                messagebox.showerror("Błąd", f"Błąd interfejsu: {str(e)}")
            except Exception as e:
                messagebox.showerror("Błąd", f"Nieoczekiwany błąd: {str(e)}")

        def cancel_numbering():
            numbering_window.destroy()

        # Przyciski
        button_frame = tk.Frame(numbering_window)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)

        tk.Button(button_frame, text="Zastosuj", command=apply_numbering,
                 bg="lightgreen", width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Anuluj", command=cancel_numbering,
                 bg="lightcoral", width=10).pack(side=tk.LEFT, padx=5)

        # Focus i bindy
        start_line_entry.focus_set()
        start_line_entry.bind('<Return>', lambda e: apply_numbering())

        # Dodaj tooltips (podpowiedzi)
        def create_tooltip(widget, text):
            def on_enter(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = tk.Label(tooltip, text=text, background="yellow", font=("Arial", 8))
                label.pack()
                widget.tooltip = tooltip

            def on_leave(event):
                if hasattr(widget, 'tooltip'):
                    widget.tooltip.destroy()
                    del widget.tooltip

            widget.bind('<Enter>', on_enter)
            widget.bind('<Leave>', on_leave)

        # Dodaj podpowiedzi
        create_tooltip(start_line_entry, "Pierwsza linia do numerowania")
        create_tooltip(end_line_entry, "Ostatnia linia do numerowania")
        create_tooltip(separator_entry, "Znak po numerze (np. . : ) -)")
        create_tooltip(spacing_entry, "Ile spacji między numerem a tekstem")
        
    def toggle_duplicates(self):
        """Przełącza duplikaty z opcją minimalnej długości słów"""
        if not self.show_duplicates:
            # Okno dialogowe dla długości duplikatów
            duplicate_window = tk.Toplevel(self.root)
            duplicate_window.title("Ustawienia duplikatów")
            duplicate_window.geometry("300x150")
            duplicate_window.transient(self.root)
            duplicate_window.grab_set()
            
            tk.Label(duplicate_window, text="Powyżej ilu znaków pokazywać duplikaty?").pack(pady=10)
            
            min_length_var = tk.IntVar(value=3)
            entry = tk.Entry(duplicate_window, textvariable=min_length_var, width=10)
            entry.pack(pady=5)
            entry.focus_set()
            
            def apply_duplicates():
                self.min_duplicate_length = min_length_var.get()
                self.show_duplicates = True
                self.duplicate_button.config(bg='lightgreen')
                self.highlight_duplicates()
                duplicate_window.destroy()
                
            def cancel_duplicates():
                duplicate_window.destroy()
            
            button_frame = tk.Frame(duplicate_window)
            button_frame.pack(pady=10)
            
            tk.Button(button_frame, text="OK", command=apply_duplicates).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="Anuluj", command=cancel_duplicates).pack(side=tk.LEFT, padx=5)
            
            entry.bind('<Return>', lambda e: apply_duplicates())
            
        else:
            # Wyłącz duplikaty
            self.show_duplicates = False
            self.duplicate_button.config(bg='SystemButtonFace')
            self.text_area.tag_remove("duplicate", "1.0", tk.END)
            
    def highlight_duplicates(self):
        if not self.show_duplicates:
            return
            
        text = self.text_area.get('1.0', tk.END)
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filtruj słowa według minimalnej długości
        filtered_words = [w for w in words if len(w) >= self.min_duplicate_length]
        word_counts = Counter(filtered_words)
        duplicates = {word for word, count in word_counts.items() if count > 1}
        
        self.text_area.tag_remove("duplicate", "1.0", tk.END)
        
        for word in duplicates:
            start = '1.0'
            while True:
                start = self.text_area.search(word, start, tk.END, nocase=True)
                if not start:
                    break
                end = f"{start}+{len(word)}c"
                self.text_area.tag_add("duplicate", start, end)
                start = end
                
        self.text_area.tag_config("duplicate", background='lightgreen')
        
    def search_text(self, event=None):
        search_term = self.search_entry.get()
        if not search_term:
            return
            
        # Usuń poprzednie zaznaczenia wyszukiwania
        self.text_area.tag_remove("search_highlight", "1.0", tk.END)
        
        text = self.text_area.get('1.0', tk.END)
        count = text.lower().count(search_term.lower())
        
        if count > 0:
            # Zaznacz wszystkie wystąpienia
            start = '1.0'
            while True:
                start = self.text_area.search(search_term, start, tk.END, nocase=True)
                if not start:
                    break
                end = f"{start}+{len(search_term)}c"
                self.text_area.tag_add("search_highlight", start, end)
                start = end
            
            # Konfiguracja podświetlenia wyszukiwania
            self.text_area.tag_config("search_highlight", background='yellow', foreground='red')
            
            # Przejdź do pierwszego wystąpienia
            first_pos = self.text_area.search(search_term, '1.0', tk.END, nocase=True)
            if first_pos:
                self.text_area.mark_set(tk.INSERT, first_pos)
                self.text_area.see(tk.INSERT)
                
        messagebox.showinfo("Wyszukiwanie", f"Znaleziono {count} wystąpień")
        
    def show_report(self):
        text = self.text_area.get('1.0', tk.END)
        if not text.strip():
            messagebox.showwarning("Uwaga", "Brak tekstu do analizy!")
            return

        # Podstawowe statystyki
        lines = text.split('\n')
        # Usuń ostatnią pustą linię jeśli istnieje
        if lines and lines[-1] == '':
            lines = lines[:-1]

        words = re.findall(r'\b\w+\b', text.lower())
        word_counts = Counter(words)

        # 1. Liczba wielkich liter
        uppercase_count = sum(1 for c in text if c.isupper())

        # 2. Liczba znaków # (hash)
        hash_count = text.count('#')

        # 3. Najwięcej duplikatów słów dłuższych niż 4 litery
        long_words = [w for w in words if len(w) > 4]
        long_word_counts = Counter(long_words)
        most_duplicated_long = long_word_counts.most_common(1)

        # 4. Wszystkie używane linie (niepuste)
        used_lines = len([line for line in lines if line.strip()])

        # 5. Wszystkich znaków w tekście (bez końcowego \n)
        total_chars = len(text) - 1 if text.endswith('\n') else len(text)

        # 6. Analiza liter z procentami - polskie litery
        polish_letters = 'AĄBCĆDEĘFGHIJKLŁMNŃOÓPQRSŚTUVWXYZŹŻ'
        special_chars = '#-,.'
        all_chars = polish_letters + special_chars

        char_counts = {}
        for char in all_chars:
            count_upper = text.upper().count(char)
            char_counts[char] = count_upper

        # Oblicz procenty
        total_letters = sum(char_counts.values())

        # Formatowanie statystyk liter w wierszach po 6
        letter_stats = []
        chars_per_row = 6

        for i in range(0, len(all_chars), chars_per_row):
            row_chars = all_chars[i:i+chars_per_row]
            row_stats = []

            for char in row_chars:
                count = char_counts[char]
                percent = (count / total_letters * 100) if total_letters > 0 else 0
                row_stats.append(f"{char}({percent:.0f}%):{count:07d}")

            # Dopełnij wiersz do 6 elementów jeśli potrzeba
            while len(row_stats) < chars_per_row:
                row_stats.append(" " * 15)

            letter_stats.append(" |".join(row_stats) + " |")

        # 7. Dodatkowe statystyki kluczowe
        # Średnia długość słowa
        avg_word_length = sum(len(w) for w in words) / len(words) if words else 0

        # Najdłuższe słowo
        longest_word = max(words, key=len) if words else "brak"

        # Najkrótsze słowo
        shortest_word = min(words, key=len) if words else "brak"

        # Stosunek wielkich do małych liter
        lowercase_count = sum(1 for c in text if c.islower())
        upper_lower_ratio = (uppercase_count / lowercase_count) if lowercase_count > 0 else 0

        # Najczęstsza litera
        letter_only_counts = {k: v for k, v in char_counts.items() if k.isalpha() and v > 0}
        most_common_letter = max(letter_only_counts.items(), key=lambda x: x[1]) if letter_only_counts else ("brak", 0)

        # Gęstość tekstu (znaki na linię)
        text_density = total_chars / used_lines if used_lines > 0 else 0

        # Tworzenie raportu
        report = f"""RAPORT STATYSTYCZNY TEKSTDYRYGENT

PODSTAWOWE STATYSTYKI:
Liczba wszystkich wielkich liter w notatniku: {uppercase_count}
Ile jest wszystkich (#) w notatniku: {hash_count}
Najwięcej duplikatów jakie słowo więcej niż 4 litery może mieć: {most_duplicated_long[0][0] if most_duplicated_long else 'brak'} ({most_duplicated_long[0][1] if most_duplicated_long else 0} wystąpień)
Wszystkie używane linie: {used_lines}
Wszystkich znaków w tekście: {total_chars}

ANALIZA LITER Z PROCENTAMI:
{chr(10).join(letter_stats)}

DODATKOWE STATYSTYKI KLUCZOWE:
Średnia długość słowa: {avg_word_length:.1f} znaków
Najdłuższe słowo: "{longest_word}" ({len(longest_word)} znaków)
Najkrótsze słowo: "{shortest_word}" ({len(shortest_word)} znaków)
Stosunek wielkich do małych liter: {upper_lower_ratio:.2f}
Najczęstsza litera: {most_common_letter[0]} ({most_common_letter[1]} wystąpień)
Gęstość tekstu: {text_density:.1f} znaków na linię

POZOSTAŁE INFORMACJE:
Wszystkich linii (z pustymi): {len(lines)}
Liczba różnych słów: {len(word_counts)}
Liczba spacji: {text.count(' ')}
Najdłuższa linia: {max(range(len(lines)), key=lambda i: len(lines[i])) + 1 if lines else 0}
"""

        # Okno raportu - większe dla więcej danych
        report_window = tk.Toplevel(self.root)
        report_window.title("Raport Statystyczny - TekstDyrygent")
        report_window.geometry("900x700")

        # Scrollbar dla długiego raportu
        frame = tk.Frame(report_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier New", 9))
        scrollbar = tk.Scrollbar(frame)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=text_widget.yview)

        text_widget.insert('1.0', report)
        text_widget.config(state='disabled')

        # Przyciski
        button_frame = tk.Frame(report_window)
        button_frame.pack(pady=5)

        tk.Button(button_frame, text="Zapisz raport",
                 command=lambda: self.save_report(report),
                 bg="lightgreen", width=15).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Zamknij",
                 command=report_window.destroy,
                 bg="lightcoral", width=15).pack(side=tk.LEFT, padx=5)
        
    def save_report(self, report):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Pliki tekstowe", "*.txt")]
        )
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(report)
            messagebox.showinfo("Info", "Raport zapisany")
            
    def update_status(self, event=None):
        # Pozycja kursora
        cursor_pos = self.text_area.index(tk.INSERT)
        line, column = cursor_pos.split('.')

        # Liczba znaków
        total_chars = len(self.text_area.get('1.0', tk.END)) - 1  # -1 dla końcowego \n

        # Zaznaczenie
        try:
            selection = self.text_area.get(tk.SEL_FIRST, tk.SEL_LAST)
            selected_chars = len(selection)
        except tk.TclError:
            selected_chars = 0

        self.status_bar.config(
            text=f"Znaków: {total_chars} | Linia: {line} | Kolumna: {int(column)+1} | Zaznaczenie: {selected_chars}"
        )

        # Aktualizacja duplikatów z opóźnieniem
        if self.show_duplicates:
            self.root.after(200, self.highlight_duplicates)  # Zwiększone opóźnienie dla lepszej wydajności
            
    def show_help(self):
        help_text = """INSTRUKCJA TEKSTDYRYGENT

SKRÓTY KLAWISZOWE:
Ctrl+F - pogrub zaznaczenie
Ctrl+I - kursywa
Ctrl+Y/R/B/G/P - kolory (żółty/czerwony/niebieski/zielony/fioletowy)
Ctrl+0 - usuń formatowanie
Ctrl+Alt+F - pogrub cały tekst
Ctrl+Q - przejdź na początek
Ctrl+W/S - skacz między słowami
F5 - szybki zapis
Ctrl+Z - cofnij (do 3 kroków)
Ctrl+Shift+Z - ponów

MENU:
Pliki → Otwórz, Zapisz, Szybki zapis (F5), Zakończ
Edycja → Cofnij, Ponów
Info → Instrukcja

KOMENDY:
/del(0):słowo - usuń słowo
/cha(nowe):stare - zamień stare na nowe
/spc(1):3 - zamień potrójne spacje na pojedyncze
/aka(4-8):2 - dodaj wcięcia
/dua(0): - przełącz duplikaty
/lin(2-5):y - koloruj linie
/lit(3-6):r - koloruj tekst
/bol(4-7): - pogrub linie

FUNKCJE:
- Przycisk "Font" - wybór fontów systemowych i z folderu "fonts"
- Przycisk "Kolor kursora" - zmiana koloru kursora
- Przycisk "Numeracja" - numeruje określone linie tekstu
- Przycisk "Raport" - szczegółowe statystyki tekstu
- Paski komend i przycisków są zawsze widoczne
"""
        messagebox.showinfo("Instrukcja", help_text)
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TekstDyrygent()
    app.run()
